public with sharing class <PERSON><PERSON>_JPN_AccountProfileController
{
    /**
     * @description Get Account Profile record ID by Account ID using Selector pattern
     * @param accountId The Account ID to search for
     * @return String Profile ID or null if not found
     */
    @AuraEnabled(cacheable=true)
    public static String getProfileIdByAccount(Id accountId)
    {
        try
        {
            if (accountId == null)
            {
                return null;
            }
            
            // Use selector class to query profiles
            List<SW_Account_Profile__c> profiles = SW_AccountProfilesSelector.newInstance()
                .selectByAccountIds(new Set<Id>{accountId});
            
            return profiles.isEmpty() ? null : profiles[0].Id;
            
        }
        catch (Exception e)
        {
            throw new AuraHandledException('Error retrieving profile: ' + e.getMessage());
        }
    }
    
    /**
     * @description Get the most recent related hospital for a store account
     * @param accountId The Store Account ID
     * @return Id The Hospital Account ID or null if not found
     */
    @AuraEnabled(cacheable=true)
    public static Id getMostRecentRelatedHospital(Id accountId)
    {
        try
        {
            if (accountId == null)
            {
                return null;
            }

            // Query the most recent hospital based on StoreHospitalRelation's LastModifiedDate
            List<JJ_JPN_StoreHospitalRelation__c> relations = [
                SELECT JJ_JPN_Hospital__c, LastModifiedDate
                FROM JJ_JPN_StoreHospitalRelation__c
                WHERE JJ_JPN_Store__c = :accountId
                AND JJ_JPN_Hospital__c != null
                WITH SECURITY_ENFORCED
                ORDER BY LastModifiedDate DESC
                LIMIT 1
            ];

            return relations.isEmpty() ? null : relations[0].JJ_JPN_Hospital__c;

        }
        catch (Exception ex)
        {
            // Silent failure for non-critical background operation
            return null;
        }
    }

    /**
     * @description Update the hospital field in the Account Profile record only if it has changed
     * @param profileId The Account Profile record ID
     * @param accountId The Store Account ID
     * @return Boolean True if update was successful or not needed, false if error occurred
     */
    @AuraEnabled
    public static Boolean updateHospitalField(Id profileId, Id accountId)
    {
        try
        {
            if (profileId == null || accountId == null)
            {
                return false;
            }

            // Get the most recent hospital
            Id hospitalId = getMostRecentRelatedHospital(accountId);

            // Validate update permissions
            if (!Schema.sObjectType.SW_Account_Profile__c.isUpdateable())
            {
                return false;
            }

            // Get the current profile record to check if update is needed
            SW_Account_Profile__c currentProfile = [
                SELECT Id, JJ_JPN_Related_Hospital__c
                FROM SW_Account_Profile__c
                WHERE Id = :profileId
                WITH SECURITY_ENFORCED
                LIMIT 1
            ];

            // Only update if the hospital has actually changed
            if (currentProfile.JJ_JPN_Related_Hospital__c != hospitalId)
            {
                currentProfile.JJ_JPN_Related_Hospital__c = hospitalId;
                
                // Use Security.stripInaccessible for secure update
                SObjectAccessDecision decision = Security.stripInaccessible(
                    AccessType.UPDATABLE,
                    new List<SW_Account_Profile__c>{currentProfile}
                );
                
                List<SW_Account_Profile__c> recordsToUpdate = (List<SW_Account_Profile__c>)decision.getRecords();
                if (!recordsToUpdate.isEmpty())
                {
                    update recordsToUpdate;
                }
            }

            return true;

        }
        catch (Exception ex)
        {
            // Silent failure for non-critical background operation
            return false;
        }
    }

    /**
     * @description Save Account Profile record with comprehensive security validation
     * Includes contact field validation and data integrity checks
     * @param profile The Account Profile record to save
     * @return String Success message
     */
    @AuraEnabled
    public static String saveProfile(SW_Account_Profile__c profile)
    {
        try
        {
            // Input validation
            if (profile == null)
            {
                throw new AuraHandledException('Profile data is required');
            }
            
            // Validate contact field selections if present
            validateProfileContactFields(profile);
            
            // Check if this is an update or insert
            Boolean isUpdate = profile.Id != null;
            
            if (isUpdate)
            {
                // Validate update permissions and existing record access
                validateProfileUpdateAccess(profile.Id);
                
                // Strip inaccessible fields before update
                SObjectAccessDecision decision = Security.stripInaccessible(
                    AccessType.UPDATABLE, 
                    new List<SW_Account_Profile__c>{profile}
                );
                
                List<SW_Account_Profile__c> recordsToUpdate = (List<SW_Account_Profile__c>)decision.getRecords();
                
                if (recordsToUpdate.isEmpty())
                {
                    throw new AuraHandledException('No fields available for update due to security restrictions');
                }
                
                // Security already enforced via stripInaccessible
                update recordsToUpdate;
                
            }
            else
            {
                // Validate create permissions
                if (!Schema.sObjectType.SW_Account_Profile__c.isCreateable())
                {
                    throw new AuraHandledException('Insufficient permissions to create Account Profile');
                }
                
                // Strip inaccessible fields before insert
                SObjectAccessDecision decision = Security.stripInaccessible(
                    AccessType.CREATABLE, 
                    new List<SW_Account_Profile__c>{profile}
                );
                
                List<SW_Account_Profile__c> recordsToInsert = (List<SW_Account_Profile__c>)decision.getRecords();
                
                if (recordsToInsert.isEmpty())
                {
                    throw new AuraHandledException('No fields available for creation due to security restrictions');
                }
                
                insert recordsToInsert;
            }
            
            return 'Profile saved successfully';
            
        }
        catch (DmlException e)
        {
            throw new AuraHandledException('Unable to save profile: ' + e.getDmlMessage(0));
        }
        catch (AuraHandledException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new AuraHandledException('An unexpected error occurred while saving the profile');
        }
    }
    
    /**
     * @description Validate contact field selections in profile
     * @param profile Profile record to validate
     */
    private static void validateProfileContactFields(SW_Account_Profile__c profile)
    {
        // Get account ID from profile for validation
        Id accountId = profile.JJ_JPN_Account__c;
        
        if (accountId == null)
        {
            // If no account context, skip contact validation
            return;
        }
        
        // Validate Dr contact if selected
        if (profile.JJ_JPN_Key_Doctor__c != null)
        {
            List<String> drRecordTypes = new List<String>{'JJ_JPN_Doctor'};
            if (!validateContactSelection(profile.JJ_JPN_Key_Doctor__c, accountId, drRecordTypes))
            {
                throw new AuraHandledException('Selected Dr contact is not valid for this account');
            }
        }
        
        // Validate Key Person 1 contact if selected
        if (profile.JJ_JPN_Key_Person_1__c != null)
        {
            List<String> keyPersonRecordTypes = new List<String>{'JJ_JPN_Doctor', 'Staff'};
            if (!validateContactSelection(profile.JJ_JPN_Key_Person_1__c, accountId, keyPersonRecordTypes))
            {
                throw new AuraHandledException('Selected Key Person 1 contact is not valid for this account');
            }
        }
        
        // Validate Key Person 2 contact if selected
        if (profile.JJ_JPN_Key_Person_2__c != null)
        {
            List<String> keyPersonRecordTypes = new List<String>{'JJ_JPN_Doctor', 'Staff'};
            if (!validateContactSelection(profile.JJ_JPN_Key_Person_2__c, accountId, keyPersonRecordTypes))
            {
                throw new AuraHandledException('Selected Key Person 2 contact is not valid for this account');
            }
        }
    }
    
    /**
     * @description Validate user can update specific profile record
     * @param profileId Profile ID to validate access for
     */
    private static void validateProfileUpdateAccess(Id profileId)
    {
        // Check update permissions
        if (!Schema.sObjectType.SW_Account_Profile__c.isUpdateable())
        {
            throw new AuraHandledException('Insufficient permissions to update Account Profile');
        }
        
        try
        {
            // Verify user can access the specific record
            List<SW_Account_Profile__c> accessibleProfiles = [
                SELECT Id 
                FROM SW_Account_Profile__c 
                WHERE Id = :profileId 
                WITH SECURITY_ENFORCED 
                LIMIT 1
            ];
            
            if (accessibleProfiles.isEmpty())
            {
                throw new AuraHandledException('Insufficient permissions to update this Account Profile record');
            }
        }
        catch (System.QueryException e)
        {
            throw new AuraHandledException('Insufficient permissions to update this Account Profile record');
        }
    }
    
    /**
     * @description Get filtered contacts based on account and record types for dynamic lookup fields
     * Comprehensive security validation with input sanitization and permission checks
     * @param accountId The Account ID to filter contacts by (using AccountId field)
     * @param recordTypes List of record type developer names to filter by
     * @return List<Contact> Filtered contacts with security enforced, limited to 200 records
     */
    @AuraEnabled(cacheable=true)
    public static List<Contact> getFilteredContacts(Id accountId, List<String> recordTypes)
    {
        try
        {
            // Input validation and sanitization
            if (!validateContactQueryInputs(accountId, recordTypes))
            {
                return new List<Contact>();
            }

            // Comprehensive security validation
            validateContactObjectAccess();
            validateAccountAccess(accountId);
            
            // Sanitize record type inputs to prevent SOQL injection
            List<String> sanitizedRecordTypes = sanitizeRecordTypes(recordTypes);
            
            // Validate record types exist and user has access
            validateRecordTypeAccess(sanitizedRecordTypes);

            // Execute secure query with comprehensive field-level security
            List<Contact> contacts = executeSecureContactQuery(accountId, sanitizedRecordTypes);
            
            // Post-process results with additional security checks
            contacts = filterContactsByRecordLevelSecurity(contacts);
            
            // Sort for consistent user experience
            contacts.sort(new ContactNameComparator());
            
            return contacts;

        }
        catch (System.QueryException e)
        {
            // Handle SOQL-specific errors
            throw new AuraHandledException('Database query error: Please contact your administrator');
        }
        catch (System.SecurityException e)
        {
            // Handle security-specific errors
            throw new AuraHandledException('Insufficient permissions to access contact data');
        }
        catch (AuraHandledException e)
        {
            // Re-throw AuraHandledExceptions as-is
            throw e;
        }
        catch (Exception e)
        {
            // Handle unexpected errors without exposing internal details
            throw new AuraHandledException('An unexpected error occurred. Please try again or contact your administrator');
        }
    }
    
    /**
     * @description Validate inputs for contact query
     * @param accountId Account ID to validate
     * @param recordTypes Record types to validate
     * @return Boolean True if inputs are valid
     */
    private static Boolean validateContactQueryInputs(Id accountId, List<String> recordTypes)
    {
        // Check for null or empty inputs
        if (accountId == null)
        {
            return false;
        }
        
        if (recordTypes == null || recordTypes.isEmpty())
        {
            return false;
        }
        
        // Validate accountId format
        String accountIdStr = String.valueOf(accountId);
        if (accountIdStr.length() != 15 && accountIdStr.length() != 18)
        {
            return false;
        }
        
        // Validate record types list size to prevent excessive queries
        if (recordTypes.size() > 10)
        {
            return false;
        }
        
        return true;
    }
    
    /**
     * @description Validate user access to Contact object
     */
    private static void validateContactObjectAccess()
    {
        // Check object-level permissions
        if (!Schema.sObjectType.Contact.isAccessible())
        {
            throw new AuraHandledException('Insufficient permissions to access Contact records');
        }
        
        // Check field-level permissions for required fields
        Map<String, Schema.SObjectField> contactFields = Schema.sObjectType.Contact.fields.getMap();
        
        List<String> requiredFields = new List<String>{'Id', 'Name', 'FirstName', 'LastName', 'Title', 'AccountId', 'RecordTypeId'};
        
        for (String fieldName : requiredFields)
        {
            if (contactFields.containsKey(fieldName))
            {
                Schema.DescribeFieldResult fieldDescribe = contactFields.get(fieldName).getDescribe();
                if (!fieldDescribe.isAccessible())
                {
                    throw new AuraHandledException('Insufficient permissions to access required contact fields');
                }
            }
        }
    }
    
    /**
     * @description Validate user access to specific account
     * @param accountId Account ID to validate access for
     */
    private static void validateAccountAccess(Id accountId)
    {
        try
        {
            // Check if user can access the account record
            // This will throw an exception if user doesn't have access due to sharing rules
            List<Account> accessibleAccounts = [
                SELECT Id 
                FROM Account 
                WHERE Id = :accountId 
                WITH SECURITY_ENFORCED 
                LIMIT 1
            ];
            
            if (accessibleAccounts.isEmpty())
            {
                throw new AuraHandledException('Insufficient permissions to access the specified account');
            }
        }
        catch (System.QueryException e)
        {
            throw new AuraHandledException('Insufficient permissions to access the specified account');
        }
    }
    
    /**
     * @description Sanitize record type inputs to prevent SOQL injection
     * @param recordTypes Raw record type list
     * @return List<String> Sanitized record types
     */
    private static List<String> sanitizeRecordTypes(List<String> recordTypes)
    {
        List<String> sanitizedTypes = new List<String>();
        
        // Define allowed record type patterns (alphanumeric and underscores only)
        Pattern allowedPattern = Pattern.compile('^[a-zA-Z0-9_]+$');
        
        for (String recordType : recordTypes)
        {
            if (String.isNotBlank(recordType))
            {
                // Remove any potential SQL injection characters
                String sanitized = recordType.trim();
                
                // Validate against allowed pattern
                if (allowedPattern.matcher(sanitized).matches() && sanitized.length() <= 80)
                {
                    sanitizedTypes.add(sanitized);
                }
                // Invalid record type format rejected - no action needed
            }
        }
        
        if (sanitizedTypes.isEmpty())
        {
            throw new AuraHandledException('No valid record types provided');
        }
        
        return sanitizedTypes;
    }
    
    /**
     * @description Test method to check available record types
     * @return List<String> Available Contact record type developer names
     */
    @AuraEnabled(cacheable=true)
    public static List<String> getAvailableContactRecordTypes()
    {
        try
        {
            List<RecordType> recordTypes = [
                SELECT Id, DeveloperName, Name, IsActive 
                FROM RecordType 
                WHERE SObjectType = 'Contact' 
                AND IsActive = true
                WITH SECURITY_ENFORCED
                ORDER BY Name
            ];
            
            List<String> developerNames = new List<String>();
            for (RecordType rt : recordTypes)
            {
                developerNames.add(rt.DeveloperName);
            }
            
            return developerNames;
        }
        catch (Exception e)
        {
            return new List<String>();
        }
    }

    /**
     * @description Validate record types exist and are accessible
     * @param recordTypes Sanitized record types to validate
     */
    private static void validateRecordTypeAccess(List<String> recordTypes)
    {
        try
        {
            // Verify record types exist and are active
            List<RecordType> validRecordTypes = [
                SELECT Id, DeveloperName, IsActive 
                FROM RecordType 
                WHERE SObjectType = 'Contact' 
                AND DeveloperName IN :recordTypes 
                AND IsActive = true
                WITH SECURITY_ENFORCED
            ];
            
            if (validRecordTypes.isEmpty())
            {
                // Instead of throwing an error, we'll query all contacts for this account
                // This provides a fallback when record types don't exist
                return;
            }
            
            // Check if all requested record types were found
            Set<String> foundTypes = new Set<String>();
            for (RecordType rt : validRecordTypes)
            {
                foundTypes.add(rt.DeveloperName);
            }
        }
        catch (System.QueryException e)
        {
            throw new AuraHandledException('Unable to validate record types: insufficient permissions');
        }
    }
    
    /**
     * @description Execute secure contact query with comprehensive security
     * @param accountId Account ID for filtering
     * @param recordTypes Validated record types
     * @return List<Contact> Query results
     */
    private static List<Contact> executeSecureContactQuery(Id accountId, List<String> recordTypes)
    {
        try
        {
            // Use static SOQL with bind variables to prevent injection
            return [
                SELECT Id, Name, FirstName, LastName, Title, AccountId, RecordType.DeveloperName
                FROM Contact
                WHERE AccountId = :accountId
                AND RecordType.DeveloperName IN :recordTypes
                AND IsDeleted = false
                WITH SECURITY_ENFORCED
                LIMIT 200
            ];
        }
        catch (Exception e)
        {
            // Fallback: Query all contacts for the account if record type filtering fails
            return [
                SELECT Id, Name, FirstName, LastName, Title, AccountId
                FROM Contact
                WHERE AccountId = :accountId
                AND IsDeleted = false
                WITH SECURITY_ENFORCED
                LIMIT 200
            ];
        }
    }
    
    /**
     * @description Apply additional record-level security filtering
     * @param contacts Raw query results
     * @return List<Contact> Filtered results
     */
    private static List<Contact> filterContactsByRecordLevelSecurity(List<Contact> contacts)
    {
        List<Contact> filteredContacts = new List<Contact>();
        
        for (Contact contact : contacts)
        {
            // Additional validation for data integrity
            if (String.isNotBlank(contact.Name) && contact.AccountId != null)
            {
                filteredContacts.add(contact);
            }
            // Contacts with invalid data are skipped
        }
        return filteredContacts;
    }

    /**
     * @description Get a single contact by ID with comprehensive security validation
     * Used for validating selected contacts and loading contact details
     * @param contactId The Contact ID to retrieve
     * @return Contact The contact record or null if not accessible
     */
    @AuraEnabled(cacheable=true)
    public static Contact getContactById(Id contactId)
    {
        try
        {
            // Input validation
            if (contactId == null)
            {
                return null;
            }
            
            // Validate contact ID format
            String contactIdStr = String.valueOf(contactId);
            if (contactIdStr.length() != 15 && contactIdStr.length() != 18)
            {
                throw new AuraHandledException('Invalid contact ID format');
            }
            
            // Validate user has access to Contact object
            validateContactObjectAccess();
            
            // Execute secure query
            List<Contact> contacts = [
                SELECT Id, Name, Title, AccountId, RecordType.DeveloperName
                FROM Contact 
                WHERE Id = :contactId 
                AND IsDeleted = false
                WITH SECURITY_ENFORCED 
                LIMIT 1
            ];
            
            if (contacts.isEmpty())
            {
                return null;
            }
            
            Contact contact = contacts[0];
            
            // Additional data integrity validation
            if (String.isBlank(contact.Name))
            {
                return null;
            }
            
            return contact;
            
        }
        catch (System.QueryException e)
        {
            throw new AuraHandledException('Unable to access contact: insufficient permissions');
        }
        catch (AuraHandledException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new AuraHandledException('An unexpected error occurred while retrieving contact');
        }
    }
    
    /**
     * @description Validate contact selection against account and record type constraints
     * Used to ensure selected contacts are valid for the specific field context
     * @param contactId The selected contact ID
     * @param accountId The account context
     * @param allowedRecordTypes List of allowed record types for this field
     * @return Boolean True if contact is valid for selection
     */
    @AuraEnabled
    public static Boolean validateContactSelection(Id contactId, Id accountId, List<String> allowedRecordTypes)
    {
        try
        {
            // Input validation
            if (contactId == null || accountId == null || allowedRecordTypes == null || allowedRecordTypes.isEmpty())
            {
                return false;
            }
            
            // Validate inputs using existing methods
            if (!validateContactQueryInputs(accountId, allowedRecordTypes))
            {
                return false;
            }
            
            // Validate security access
            validateContactObjectAccess();
            validateAccountAccess(accountId);
            
            // Sanitize record types
            List<String> sanitizedRecordTypes = sanitizeRecordTypes(allowedRecordTypes);
            
            // Query to validate contact meets all criteria
            List<Contact> validContacts = [
                SELECT Id 
                FROM Contact 
                WHERE Id = :contactId 
                AND AccountId = :accountId 
                AND RecordType.DeveloperName IN :sanitizedRecordTypes 
                AND IsDeleted = false
                WITH SECURITY_ENFORCED 
                LIMIT 1
            ];
            
            return !validContacts.isEmpty();
            
        }
        catch (Exception e)
        {
            return false;
        }
    }
    
    /**
     * @description Comparator class for consistent contact name sorting
     * Handles null values and provides case-insensitive sorting
     */
    public class ContactNameComparator implements Comparator<Contact>
    {
        public Integer compare(Contact contact1, Contact contact2)
        {
            String name1 = contact1.Name != null ? contact1.Name.toLowerCase() : '';
            String name2 = contact2.Name != null ? contact2.Name.toLowerCase() : '';
            
            if (name1 < name2)
            {
                return -1;
            }
            if (name1 > name2)
            {
                return 1;
            }
            return 0;
        }
    }

    /**
     * @description Get a profile record by ID with contact field values
     * @param profileId The profile record ID
     * @return SW_Account_Profile__c The profile record with contact fields
     */
    @AuraEnabled(cacheable=true)
    public static SW_Account_Profile__c getProfileRecord(Id profileId)
    {
        try
        {
            if (profileId == null)
            {
                return null;
            }
            
            // Validate user has access to the profile record
            if (!Schema.sObjectType.SW_Account_Profile__c.isAccessible())
            {
                throw new AuraHandledException('Insufficient permissions to access Account Profile records');
            }
            
            // Query the profile record with contact fields
            List<SW_Account_Profile__c> profiles = [
                SELECT Id, JJ_JPN_Key_Doctor__c, JJ_JPN_Key_Person_1__c, JJ_JPN_Key_Person_2__c
                FROM SW_Account_Profile__c 
                WHERE Id = :profileId 
                WITH SECURITY_ENFORCED 
                LIMIT 1
            ];
            
            return profiles.isEmpty() ? null : profiles[0];
            
        }
        catch (System.QueryException e)
        {
            throw new AuraHandledException('Unable to access profile record: insufficient permissions');
        }
        catch (Exception e)
        {
            throw new AuraHandledException('An unexpected error occurred while retrieving profile record');
        }
    }

    /**
     * @description Get an account record by ID (for hospital name loading)
     * @param accountId The Account ID to retrieve
     * @return Account The account record or null if not accessible
     */
    @AuraEnabled(cacheable=true)
    public static Account getAccountById(Id accountId)
    {
        try
        {
            // Basic null check
            if (accountId == null) {
                return null;
            }

            // Validate id format
            if (!isValidIdFormat(accountId)) {
                throw new AuraHandledException('Invalid account ID format');
            }

            // Validate object-level access (throws AuraHandledException on failure)
            validateAccountObjectAccess();

            // Perform the secure query and return the first result (or null)
            return fetchAccountByIdSecure(accountId);
        }
        catch (System.QueryException e)
        {
            throw new AuraHandledException('Unable to access account: insufficient permissions');
        }
        catch (AuraHandledException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new AuraHandledException('An unexpected error occurred while retrieving account');
        }
    }

    // Simple Id format check (15 or 18 chars)
    private static Boolean isValidIdFormat(Id recordId) 
    {
        if (recordId == null)
        {
            return false;
        }
        String idStr = String.valueOf(recordId);
        return idStr.length() == 15 || idStr.length() == 18;
    }

    // Validates that the current user can query Account object fields used here
    // Throws AuraHandledException if access is insufficient
    private static void validateAccountObjectAccess() 
    {
        if (!Schema.sObjectType.Account.isAccessible()) 
        {
            throw new AuraHandledException('Insufficient permissions to access Account records');
        }
    }

    // Perform the actual query with security enforced and minimal branching
    private static Account fetchAccountByIdSecure(Id accountId) 
    {
        List<Account> accounts = [
            SELECT Id, Name
            FROM Account
            WHERE Id = :accountId
            AND IsDeleted = false
            WITH SECURITY_ENFORCED
            LIMIT 1
        ];

        if (accounts.isEmpty()) 
        {
            return null;
        }

        Account acct = accounts[0];

        // Data integrity check
        if (String.isBlank(acct.Name)) 
        {
            return null;
        }

        return acct;
    }

    /**
     * @description Get field accessibility information for the current user
     * Only checks fields that are actually used in the LWC component
     * @return Map<String, Object> Field accessibility data
     */
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> getFieldAccessibility()
    {
        try
        {
            Map<String, Object> fieldAccess = new Map<String, Object>();

            // Define only the fields that are actually used in the LWC component
            Set<String> fieldsUsedInComponent = new Set<String>
            {
                // Profile Target and Phase fields
                'JJ_JPN_Picklist_1__c',
                'JJ_JPN_Teikibin_Phase__c',
                'JJ_JPN_Teikibin_Recommend_Target__c',
                'JJ_JPN_MF_Phase__c',
                'JJ_JPN_MF_Recommend_Target__c',
                'JJ_JPN_AST_Phase__c',
                'JJ_JPN_AST_Recommend_Target__c',

                // Total patients fields
                'JJ_JPN_Number_1__c',
                'JJ_JPN_Number_2__c',
                'JJ_JPN_Number_3__c',

                // Situation and needs fields
                'JJ_JPN_Picklist_2__c',
                'JJ_JPN_Picklist_3__c',
                'JJ_JPN_Text_1__c',
                'JJ_JPN_Text_2__c',

                // Share fields - ALL Brand (Percent 1-7)
                'JJ_JPN_Percent_1__c', 'JJ_JPN_Percent_2__c', 'JJ_JPN_Percent_3__c',
                'JJ_JPN_Percent_4__c', 'JJ_JPN_Percent_5__c', 'JJ_JPN_Percent_6__c',
                'JJ_JPN_Percent_7__c',

                // Share fields - DD (Percent 8-14)
                'JJ_JPN_Percent_8__c', 'JJ_JPN_Percent_9__c', 'JJ_JPN_Percent_10__c',
                'JJ_JPN_Percent_11__c', 'JJ_JPN_Percent_12__c', 'JJ_JPN_Percent_13__c',
                'JJ_JPN_Percent_14__c',

                // Share fields - RU (Percent 15-21)
                'JJ_JPN_Percent_15__c', 'JJ_JPN_Percent_16__c', 'JJ_JPN_Percent_17__c',
                'JJ_JPN_Percent_18__c', 'JJ_JPN_Percent_19__c', 'JJ_JPN_Percent_20__c',
                'JJ_JPN_Percent_21__c',

                // Share fields - AST (Percent 22-28)
                'JJ_JPN_Percent_22__c', 'JJ_JPN_Percent_23__c', 'JJ_JPN_Percent_24__c',
                'JJ_JPN_Percent_25__c', 'JJ_JPN_Percent_26__c', 'JJ_JPN_Percent_27__c',
                'JJ_JPN_Percent_28__c',

                // Share fields - MF (Percent 29-35)
                'JJ_JPN_Percent_29__c', 'JJ_JPN_Percent_30__c', 'JJ_JPN_Percent_31__c',
                'JJ_JPN_Percent_32__c', 'JJ_JPN_Percent_33__c', 'JJ_JPN_Percent_34__c',
                'JJ_JPN_Percent_35__c',

                // Share fields - Beauty (Percent 36-42)
                'JJ_JPN_Percent_36__c', 'JJ_JPN_Percent_37__c', 'JJ_JPN_Percent_38__c',
                'JJ_JPN_Percent_39__c', 'JJ_JPN_Percent_40__c', 'JJ_JPN_Percent_41__c',
                'JJ_JPN_Percent_42__c',

                // Brand fields (Picklist 4-18)
                'JJ_JPN_Picklist_4__c', 'JJ_JPN_Picklist_5__c', 'JJ_JPN_Picklist_6__c',
                'JJ_JPN_Picklist_7__c', 'JJ_JPN_Picklist_8__c', 'JJ_JPN_Picklist_9__c',
                'JJ_JPN_Picklist_10__c', 'JJ_JPN_Picklist_11__c', 'JJ_JPN_Picklist_12__c',
                'JJ_JPN_Picklist_13__c', 'JJ_JPN_Picklist_14__c', 'JJ_JPN_Picklist_15__c',
                'JJ_JPN_Picklist_16__c', 'JJ_JPN_Picklist_17__c', 'JJ_JPN_Picklist_18__c'
            };

            Map<String, Schema.SObjectField> fieldMap = Schema.SObjectType.SW_Account_Profile__c.fields.getMap();

            // Only check fields that are actually used in the component
            for (String fieldName : fieldsUsedInComponent)
            {
                if (fieldMap.containsKey(fieldName))
                {
                    Schema.DescribeFieldResult fieldDescribe = fieldMap.get(fieldName).getDescribe();

                    fieldAccess.put(fieldName.toLowerCase(), new Map<String, Object>
                    {
                        'isAccessible' => fieldDescribe.isAccessible(),
                        'isCreateable' => fieldDescribe.isCreateable(),
                        'isUpdateable' => fieldDescribe.isUpdateable(),
                        'label' => fieldDescribe.getLabel(),
                        'type' => fieldDescribe.getType().name()
                    });
                }
            }

            return fieldAccess;

        }
        catch (Exception e)
        {
            throw new AuraHandledException('Error retrieving field accessibility: ' + e.getMessage());
        }
    }
}