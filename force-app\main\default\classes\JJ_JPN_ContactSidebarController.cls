public with sharing class <PERSON><PERSON><PERSON><PERSON><PERSON>_ContactSidebarController
{
    // Constants for record types
    private static final String DOCTOR_RT_NAME = 'J<PERSON>_<PERSON>N_Doctor';
    private static final String STAFF_RT_NAME = 'Staff';
    private static final Integer STAFF_QUERY_LIMIT = 1000;
    
    /**
     * Get Doctor contacts related to the Account through AccountContactRelation
     */
    @AuraEnabled(cacheable=true)
    public static List<ContactWrapper> getDoctorContacts(Id accountId)
    {
        try
        {
            // Validate read access
            if (!Schema.sObjectType.AccountContactRelation.isAccessible() || 
                !Schema.sObjectType.Contact.isAccessible())
            {
                throw new AuraHandledException('Insufficient permissions to access contact relationships');
            }
            
            // Query AccountContactRelation to get doctors related to this account
            List<AccountContactRelation> acrList = [
                SELECT Id, ContactId, 
                       Contact.Id, Contact.Name, Contact.Occupation__c, 
                       Contact.JJ_JPN_ULTAlmaMaterName__c, 
                       Contact.JJ_JPN_ULTGraduationYear__c, 
                       Contact.JJ_JPN_MedicalOffice__c,
                       Contact.JJ_JPN_Dr_Speciality__c, 
                       Contact.JJ_JPN_Dr_Hobby__c,
                       Contact.JJ_JPN_Dr_Name_InfluenceOn1__c, 
                       Contact.JJ_JPN_Dr_Name_InfluenceOn1__r.Name,
                       Contact.JJ_JPN_Dr_Name_InfluenceBy1__c, 
                       Contact.JJ_JPN_Dr_Name_InfluenceBy1__r.Name
                FROM AccountContactRelation 
                WHERE AccountId = :accountId 
                AND Contact.RecordType.DeveloperName = :DOCTOR_RT_NAME
                ORDER BY CreatedDate DESC
            ];
            
            // Create wrapper objects
            List<ContactWrapper> wrapperList = new List<ContactWrapper>();
            for (AccountContactRelation acr : acrList)
            {
                ContactWrapper wrapper = new ContactWrapper();
                wrapper.contact = acr.Contact;
                wrapper.accountContactRelationId = acr.Id;
                wrapperList.add(wrapper);
            }
            
            return wrapperList;
        }
        catch (Exception e)
        {
            throw new AuraHandledException('Error retrieving doctor contacts: ' + e.getMessage());
        }
    }
    
    // Wrapper class to include AccountContactRelation ID
    public class ContactWrapper
    {
        @AuraEnabled
        public Contact contact { get; set; }
        @AuraEnabled
        public Id accountContactRelationId { get; set; }
    }
    
    /**
     * Get Staff contacts related to the Account through AccountContactRelation
     * Returns all staff contacts without pagination
     */
    @AuraEnabled(cacheable=true)
    public static List<AccountContactRelation> getStaffContacts(Id accountId)
    {
        try
        {
            // Validate read access
            if (!Schema.sObjectType.AccountContactRelation.isAccessible() || 
                !Schema.sObjectType.Contact.isAccessible())
            {
                throw new AuraHandledException('Insufficient permissions to access contact relationships');
            }
            
            return [
                SELECT Id, ContactId, Contact.Id, Contact.Name, Contact.Email, Contact.Phone,
                       Contact.Occupation__c
                FROM AccountContactRelation 
                WHERE AccountId = :accountId 
                AND Contact.RecordType.DeveloperName = :STAFF_RT_NAME
                ORDER BY CreatedDate DESC
                LIMIT :STAFF_QUERY_LIMIT
            ];
        }
        catch (Exception e)
        {
            throw new AuraHandledException('Error retrieving staff contacts: ' + e.getMessage());
        }
    }
    
    /**
     * Create AccountContactRelation with RecordType validation for Doctor contacts
     */
    @AuraEnabled
    public static String createDoctorAccountContactRelation(Id contactId, Id accountId)
    {
        try
        {
            // Basic input validation
            if (contactId == null || accountId == null) {
                return 'Missing required parameters';
            }

            // Validate read access for Contact
            if (!Schema.sObjectType.Contact.isAccessible())
            {
                throw new AuraHandledException('Insufficient permissions to access contacts');
            }
            
            // Fetch contact safely
            List<Contact> contacts = [
                SELECT Id, RecordType.DeveloperName 
                FROM Contact 
                WHERE Id = :contactId 
                LIMIT 1
            ];
            
            if (contacts.isEmpty())
            {
                return 'Contact not found';
            }
            
            // Validate record type
            if (!isDoctorRecordType(contacts[0].RecordType.DeveloperName))
            {
                return 'Only Doctor contacts can be added to this relationship';
            }
            
            // Delegate relation creation (includes permission checks & duplicate check)
            return createRelationIfNotExists(accountId, contactId);
        }
        catch (Exception e)
        {
            throw new AuraHandledException('Error creating relationship: ' + e.getMessage());
        }
    }
    
    /**
     * Create AccountContactRelation with relationship type parameter
     */
    @AuraEnabled
    public static String createAccountContactRelationWithType(Id accountId, Id contactId, String relationshipType) {
        try {
            // --- Step 1: Validate inputs ---
            if (accountId == null || contactId == null || String.isBlank(relationshipType)) 
            {
                return 'Missing required parameters';
            }

            // --- Step 2: Validate read access for Contact and AccountContactRelation ---
            ensureContactAndRelationReadable();

            // --- Step 3: Get contact record type (safe list query) ---
            List<Contact> contacts = [
                SELECT Id, RecordType.DeveloperName, Name
                FROM Contact
                WHERE Id = :contactId
                LIMIT 1
            ];

            if (contacts.isEmpty()) 
            {
                return 'Contact not found';
            }

            String contactRecordType = contacts[0].RecordType.DeveloperName;

            // --- Step 4: Validate record type based on relationship type ---
            if (!isValidContactType(contactRecordType, relationshipType)) 
            {
                return 'Only ' + relationshipType + 
                    ' contacts can be added to this relationship. Selected contact has record type: ' + 
                    contactRecordType;
            }

            // --- Step 5 & 6: Create relation if not exists (includes create permission check) ---
            return createRelationIfNotExists(accountId, contactId);
        } catch (Exception e) 
        {
            return 'Error creating relationship: ' + e.getMessage();
        }
    }

    /**
     * Helper: check if the contact record type is valid for the relationship
     */
    private static Boolean isValidContactType(String contactRecordType, String relationshipType) 
    {
        if (String.isBlank(contactRecordType) || String.isBlank(relationshipType)) 
        {
            return false;
        }

        Boolean isStaffType = relationshipType == STAFF_RT_NAME;
        if (isStaffType) 
        {
            return (contactRecordType == STAFF_RT_NAME ||
                    contactRecordType == 'JJ_JPN_Staff' ||
                    contactRecordType.contains(STAFF_RT_NAME));
        }

        // Doctor type
        return (contactRecordType == DOCTOR_RT_NAME ||
                contactRecordType == 'Doctor' ||
                contactRecordType.contains('Doctor'));
    }

    /**
     * Helper: recognize doctor record type
     */
    private static Boolean isDoctorRecordType(String recordTypeDevName) 
    {
        if (String.isBlank(recordTypeDevName))
        {
        	return false;
        } 
        return (recordTypeDevName == DOCTOR_RT_NAME ||
                recordTypeDevName == 'Doctor' ||
                recordTypeDevName.contains('Doctor'));
    }

    /**
     * Helper: check if AccountContactRelation already exists
     */
    private static Boolean hasExistingRelation(Id accountId, Id contactId) 
    {
        return ![
            SELECT Id FROM AccountContactRelation 
            WHERE ContactId = :contactId AND AccountId = :accountId
            LIMIT 1
        ].isEmpty();
    }

    /**
     * Helper: create relation if not exists with permission checks
     */
    private static String createRelationIfNotExists(Id accountId, Id contactId) 
    {
        // Check read access for AccountContactRelation
        if (!Schema.sObjectType.AccountContactRelation.isAccessible()) 
        {
            throw new AuraHandledException('Insufficient permissions to access AccountContactRelation');
        }

        // Duplicate check
        if (hasExistingRelation(accountId, contactId)) 
        {
            return 'Relationship already exists';
        }

        // Validate create permission
        if (!Schema.sObjectType.AccountContactRelation.isCreateable()) 
        {
            throw new AuraHandledException('Insufficient permissions to create AccountContactRelation');
        }

        // Insert relation
        insert new AccountContactRelation(AccountId = accountId, ContactId = contactId);

        return 'Success';
    }

    /**
     * Consolidated readable permission check for Contact and AccountContactRelation
     */
    private static void ensureContactAndRelationReadable() 
    {
        if (!Schema.sObjectType.Contact.isAccessible()) 
        {
            throw new AuraHandledException('Insufficient permissions to access contacts');
        }
        if (!Schema.sObjectType.AccountContactRelation.isAccessible()) 
        {
            throw new AuraHandledException('Insufficient permissions to access AccountContactRelation');
        }
    }

    /**
     * Get Staff Record Type ID for new contact creation
     */
    @AuraEnabled(cacheable=true)
    public static String getStaffRecordTypeId()
    {
        try
        {
            // Validate read access
            if (!Schema.sObjectType.RecordType.isAccessible())
            {
                throw new AuraHandledException('Insufficient permissions to access RecordType');
            }
            
            // Try different possible staff record type names
            String staffPattern = '%Staff%';
            List<RecordType> staffRecordTypes = [
                SELECT Id, DeveloperName
                FROM RecordType 
                WHERE SObjectType = 'Contact' 
                AND (DeveloperName = :STAFF_RT_NAME OR DeveloperName = 'JJ_JPN_Staff' OR DeveloperName LIKE :staffPattern)
                LIMIT 1
            ];
            
            if (staffRecordTypes.isEmpty())
            {
                throw new AuraHandledException('No Staff record type found');
            }
            
            return staffRecordTypes[0].Id;
        }
        catch (Exception e)
        {
            throw new AuraHandledException('Staff record type not found: ' + e.getMessage());
        }
    }
    
}