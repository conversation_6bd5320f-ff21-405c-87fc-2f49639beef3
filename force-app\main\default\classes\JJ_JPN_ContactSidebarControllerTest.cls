@isTest
public class J<PERSON>_<PERSON>N_ContactSidebarControllerTest
{
    // Constants for record types
    private static final String DOCTOR_RT_NAME = 'J<PERSON>_JPN_Doctor';
    private static final String STAFF_RT_NAME = 'Staff';
    
    // Constants for duplicate string literals
    private static final String ACCOUNT_SECOND_TEST = 'Second Test Account';
    private static final String MSG_MISSING_REQUIRED_PARAMETERS = 'Missing required parameters';
    private static final String MSG_SHOULD_RETURN_MISSING_PARAMS = 'Should return missing parameters message';
    
    @TestSetup
    static void setupTestData()
    {
        // Create Account
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        // Get Record Types
        Id doctorRecordTypeId = getRecordTypeId(DOCTOR_RT_NAME);
        Id staffRecordTypeId = getRecordTypeId(STAFF_RT_NAME);
        
        // Create Doctor Contacts
        List<Contact> doctorContacts = new List<Contact>();
        for (Integer i = 1; i <= 3; i++)
        {
            Contact doctor = new Contact(
                FirstName = 'Doctor',
                LastName = 'Test ' + i,
                AccountId = testAccount.Id,
                RecordTypeId = doctorRecordTypeId,
                Email = 'doctor' + i + '@test.com',
                Phone = '123-456-789' + i
            );
            doctorContacts.add(doctor);
        }
        if (doctorRecordTypeId != null)
        {
            insert doctorContacts;
        }
        
        // Create Staff Contacts (only 8 to test limit)
        List<Contact> staffContacts = new List<Contact>();
        for (Integer i = 1; i <= 8; i++)
        {
            Contact staff = new Contact(
                FirstName = 'Staff',
                LastName = 'Test ' + i,
                AccountId = testAccount.Id,
                RecordTypeId = staffRecordTypeId,
                Email = 'staff' + i + '@test.com'
            );
            staffContacts.add(staff);
        }
        if (staffRecordTypeId != null)
        {
            insert staffContacts;
        }
    }
    
    private static Id getRecordTypeId(String developerName)
    {
        RecordTypeInfo rtInfo = Schema.SObjectType.Contact.getRecordTypeInfosByDeveloperName().get(developerName);
        return rtInfo != null ? rtInfo.getRecordTypeId() : null;
    }
    
    @isTest
    static void testGetDoctorContacts()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            
            // WHEN: Retrieving doctor contacts
            Test.startTest();
            List<JJ_JPN_ContactSidebarController.ContactWrapper> doctors = 
                JJ_JPN_ContactSidebarController.getDoctorContacts(testAccount.Id);
            Test.stopTest();
            
            // THEN: Should return doctor contact wrappers
            Assert.isTrue(doctors.size() >= 0, 'Should return doctor contact wrappers');
            if (doctors.size() > 0)
            {
                Assert.isNotNull(doctors[0].contact, 'Contact should not be null');
            }
        }
    }
    
    @isTest
    static void testGetStaffContacts()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            
            // WHEN: Retrieving staff contacts
            Test.startTest();
            List<AccountContactRelation> staff = JJ_JPN_ContactSidebarController.getStaffContacts(testAccount.Id);
            Test.stopTest();
            
            // THEN: Should return staff contact relations
            Assert.isTrue(staff.size() >= 0, 'Should return staff contact relations');
            // Verify we get the expected number of staff contacts (8 created in setup)
            Assert.areEqual(8, staff.size(), 'Should return all staff contacts');
        }
    }
    
    @isTest
    static void testCreateAccountContactRelation()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            Account secondAccount = new Account(Name = ACCOUNT_SECOND_TEST);
            insert secondAccount;
            
            // Create a doctor contact related to second account
            Id doctorRecordTypeId = getRecordTypeId(DOCTOR_RT_NAME);
            Contact unrelatedContact = new Contact(
                FirstName = 'Unrelated',
                LastName = 'Contact',
                AccountId = secondAccount.Id,
                RecordTypeId = doctorRecordTypeId,
                Email = '<EMAIL>'
            );
            insert unrelatedContact;
            
            // WHEN: Creating account contact relation
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createDoctorAccountContactRelation(
                unrelatedContact.Id, testAccount.Id);
            Test.stopTest();
            
            // THEN: Should create relationship successfully
            Assert.areEqual('Success', result, 'Should create relationship successfully');
            
            // Verify relationship was created
            List<AccountContactRelation> relations = [
                SELECT Id FROM AccountContactRelation 
                WHERE ContactId = :unrelatedContact.Id AND AccountId = :testAccount.Id
            ];
            Assert.areEqual(1, relations.size(), 'AccountContactRelation should be created');
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationWithType()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            Account secondAccount = new Account(Name = ACCOUNT_SECOND_TEST);
            insert secondAccount;
            
            // Create a staff contact related to second account
            Id staffRecordTypeId = getRecordTypeId(STAFF_RT_NAME);
            Contact unrelatedStaff = new Contact(
                FirstName = 'Unrelated',
                LastName = 'Staff',
                AccountId = secondAccount.Id,
                RecordTypeId = staffRecordTypeId,
                Email = '<EMAIL>'
            );
            insert unrelatedStaff;
            
            // WHEN: Creating account contact relation with type
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, unrelatedStaff.Id, 'Staff');
            Test.stopTest();
            
            // THEN: Should create staff relationship successfully
            Assert.areEqual('Success', result, 'Should create staff relationship successfully: ' + result);
            
            // Verify relationship was created
            List<AccountContactRelation> relations = [
                SELECT Id FROM AccountContactRelation 
                WHERE ContactId = :unrelatedStaff.Id AND AccountId = :testAccount.Id
            ];
            Assert.areEqual(1, relations.size(), 'AccountContactRelation should be created');
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationInvalidType()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            Account secondAccount = new Account(Name = ACCOUNT_SECOND_TEST);
            insert secondAccount;
            
            // Create a doctor contact and try to add as staff
            Id doctorRecordTypeId = getRecordTypeId(DOCTOR_RT_NAME);
            Contact doctorContact = new Contact(
                FirstName = 'Doctor',
                LastName = 'WrongType',
                AccountId = secondAccount.Id,
                RecordTypeId = doctorRecordTypeId,
                Email = '<EMAIL>'
            );
            insert doctorContact;
            
            // WHEN: Attempting to create relationship with wrong type
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, doctorContact.Id, 'Staff');
            Test.stopTest();
            
            // THEN: Should not create relationship for invalid contact type
            Assert.areNotEqual('Success', result, 'Should not create relationship for invalid contact type');
            Assert.isTrue(result.contains('Staff') || result.contains('record type'), 
                         'Error message should mention record type mismatch: ' + result);
        }
    }
    
    @isTest
    static void testGetStaffRecordTypeId()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            // WHEN: Retrieving staff record type ID
            Test.startTest();
            String recordTypeId = JJ_JPN_ContactSidebarController.getStaffRecordTypeId();
            Test.stopTest();
            
            // THEN: Should return staff record type ID
            Assert.isNotNull(recordTypeId, 'Should return staff record type ID');
            Assert.isTrue(recordTypeId.length() == 15 || recordTypeId.length() == 18, 
                         'Should return valid Salesforce ID');
        }
    }
    
    @isTest
    static void testCreateDoctorAccountContactRelationContactNotFound()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            
            // Create a fake contact ID that doesn't exist
            Contact tempContact = new Contact(FirstName = 'Temp', LastName = 'Contact');
            insert tempContact;
            Id fakeContactId = tempContact.Id;
            delete tempContact;
            
            // WHEN: Attempting to create relation with non-existent contact
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createDoctorAccountContactRelation(
                fakeContactId, testAccount.Id);
            Test.stopTest();
            
            // THEN: Should return contact not found message
            Assert.areEqual('Contact not found', result, 'Should return contact not found message');
        }
    }
    
    @isTest
    static void testCreateDoctorAccountContactRelationWrongRecordType()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            Account secondAccount = new Account(Name = ACCOUNT_SECOND_TEST);
            insert secondAccount;
            
            // Create a staff contact and try to add as doctor
            Id staffRecordTypeId = getRecordTypeId(STAFF_RT_NAME);
            Contact staffContact = new Contact(
                FirstName = 'Staff',
                LastName = 'WrongType',
                AccountId = secondAccount.Id,
                RecordTypeId = staffRecordTypeId,
                Email = '<EMAIL>'
            );
            insert staffContact;
            
            // WHEN: Attempting to create doctor relation with staff contact
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createDoctorAccountContactRelation(
                staffContact.Id, testAccount.Id);
            Test.stopTest();
            
            // THEN: Should not create relationship for non-doctor contact
            Assert.areNotEqual('Success', result, 'Should not create relationship for non-doctor contact');
            Assert.isTrue(result.contains('Doctor'), 'Error message should mention Doctor: ' + result);
        }
    }
    
    @isTest
    static void testCreateDoctorAccountContactRelationAlreadyExists()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            List<Contact> doctors = [SELECT Id FROM Contact WHERE RecordType.DeveloperName = :DOCTOR_RT_NAME LIMIT 1];
            
            if (!doctors.isEmpty())
            {
                // WHEN: Attempting to create duplicate relation
                Test.startTest();
                String result = JJ_JPN_ContactSidebarController.createDoctorAccountContactRelation(
                    doctors[0].Id, testAccount.Id);
                Test.stopTest();
                
                // THEN: Should return relationship exists message
                Assert.areEqual('Relationship already exists', result, 'Should return relationship exists message');
            }
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationWithTypeMissingParams()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            
            // WHEN: Attempting to create relation with missing parameters
            Test.startTest();
            String result1 = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                null, testAccount.Id, 'Staff');
            String result2 = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, null, 'Staff');
            String result3 = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, testAccount.Id, '');
            Test.stopTest();
            
            // THEN: Should return missing parameters message
            Assert.areEqual(MSG_MISSING_REQUIRED_PARAMETERS, result1, MSG_SHOULD_RETURN_MISSING_PARAMS);
            Assert.areEqual(MSG_MISSING_REQUIRED_PARAMETERS, result2, MSG_SHOULD_RETURN_MISSING_PARAMS);
            Assert.areEqual(MSG_MISSING_REQUIRED_PARAMETERS, result3, MSG_SHOULD_RETURN_MISSING_PARAMS);
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationWithTypeContactNotFound()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            
            // Create a fake contact ID that doesn't exist
            Contact tempContact = new Contact(FirstName = 'Temp', LastName = 'Contact');
            insert tempContact;
            Id fakeContactId = tempContact.Id;
            delete tempContact;
            
            // WHEN: Attempting to create relation with non-existent contact
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, fakeContactId, 'Staff');
            Test.stopTest();
            
            // THEN: Should return contact not found message
            Assert.areEqual('Contact not found', result, 'Should return contact not found message');
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationWithTypeAlreadyExists()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            List<Contact> staff = [SELECT Id FROM Contact WHERE RecordType.DeveloperName = :STAFF_RT_NAME LIMIT 1];
            
            if (!staff.isEmpty())
            {
                // WHEN: Attempting to create duplicate relation
                Test.startTest();
                String result = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                    testAccount.Id, staff[0].Id, 'Staff');
                Test.stopTest();
                
                // THEN: Should return relationship exists message
                Assert.isTrue(result.contains('already exists'), 'Should return relationship exists message: ' + result);
            }
        }
    }
    
    @isTest
    static void testGetDoctorContactsWithNoRelations()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account newAccount = new Account(Name = 'New Test Account');
            insert newAccount;
            
            // WHEN: Retrieving doctor contacts for account with no relations
            Test.startTest();
            List<JJ_JPN_ContactSidebarController.ContactWrapper> doctors = 
                JJ_JPN_ContactSidebarController.getDoctorContacts(newAccount.Id);
            Test.stopTest();
            
            // THEN: Should return empty list
            Assert.areEqual(0, doctors.size(), 'Should return empty list for account with no relations');
        }
    }
    
    @isTest
    static void testGetStaffContactsWithNoRelations()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account newAccount = new Account(Name = 'New Test Account');
            insert newAccount;
            
            // WHEN: Retrieving staff contacts for account with no relations
            Test.startTest();
            List<AccountContactRelation> staff = 
                JJ_JPN_ContactSidebarController.getStaffContacts(newAccount.Id);
            Test.stopTest();
            
            // THEN: Should return empty list
            Assert.areEqual(0, staff.size(), 'Should return empty list for account with no relations');
        }
    }
    
    @isTest
    static void testGetDoctorContactsInsufficientPermissions()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            
            // WHEN: Retrieving doctor contacts (exception path)
            Test.startTest();
            try
            {
                List<JJ_JPN_ContactSidebarController.ContactWrapper> doctors = 
                    JJ_JPN_ContactSidebarController.getDoctorContacts(testAccount.Id);
                // THEN: Should succeed or throw exception based on permissions
                Assert.isNotNull(doctors, 'Should return result');
            }
            catch (AuraHandledException e)
            {
                // THEN: Exception is acceptable for permission issues
                Assert.isTrue(e.getMessage().contains('permissions') || e.getMessage().contains('Error'), 
                             'Should handle permission errors');
            }
            Test.stopTest();
        }
    }
    
    @isTest
    static void testGetStaffContactsInsufficientPermissions()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            
            // WHEN: Retrieving staff contacts (exception path)
            Test.startTest();
            try
            {
                List<AccountContactRelation> staff = 
                    JJ_JPN_ContactSidebarController.getStaffContacts(testAccount.Id);
                // THEN: Should succeed or throw exception based on permissions
                Assert.isNotNull(staff, 'Should return result');
            }
            catch (AuraHandledException e)
            {
                // THEN: Exception is acceptable for permission issues
                Assert.isTrue(e.getMessage().contains('permissions') || e.getMessage().contains('Error'), 
                             'Should handle permission errors');
            }
            Test.stopTest();
        }
    }
    
    @isTest
    static void testCreateDoctorAccountContactRelationInsufficientPermissions()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            List<Contact> doctors = [SELECT Id FROM Contact WHERE RecordType.DeveloperName = :DOCTOR_RT_NAME LIMIT 1];
            
            if (!doctors.isEmpty())
            {
                // WHEN: Creating relation (exception path)
                Test.startTest();
                try
                {
                    String result = JJ_JPN_ContactSidebarController.createDoctorAccountContactRelation(
                        doctors[0].Id, testAccount.Id);
                    // THEN: Should succeed or throw exception based on permissions
                    Assert.isNotNull(result, 'Should return result');
                }
                catch (AuraHandledException e)
                {
                    // THEN: Exception is acceptable for permission issues
                    Assert.isTrue(e.getMessage().contains('permissions') || e.getMessage().contains('Error'), 
                                 'Should handle permission errors');
                }
                Test.stopTest();
            }
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationWithTypeInsufficientPermissions()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            List<Contact> staff = [SELECT Id FROM Contact WHERE RecordType.DeveloperName = :STAFF_RT_NAME LIMIT 1];
            
            if (!staff.isEmpty())
            {
                // WHEN: Creating relation with type (exception path)
                Test.startTest();
                try
                {
                    String result = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                        testAccount.Id, staff[0].Id, 'Staff');
                    // THEN: Should succeed or throw exception based on permissions
                    Assert.isNotNull(result, 'Should return result');
                }
                catch (AuraHandledException e)
                {
                    // THEN: Exception is acceptable for permission issues
                    Assert.isTrue(e.getMessage().contains('permissions') || e.getMessage().contains('Error'), 
                                 'Should handle permission errors');
                }
                Test.stopTest();
            }
        }
    }
    
    @isTest
    static void testGetStaffRecordTypeIdInsufficientPermissions()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            // WHEN: Getting staff record type (exception path)
            Test.startTest();
            try
            {
                String recordTypeId = JJ_JPN_ContactSidebarController.getStaffRecordTypeId();
                // THEN: Should succeed or throw exception based on permissions
                Assert.isNotNull(recordTypeId, 'Should return result');
            }
            catch (AuraHandledException e)
            {
                // THEN: Exception is acceptable for permission issues
                Assert.isTrue(e.getMessage().contains('permissions') || e.getMessage().contains('not found') || 
                             e.getMessage().contains('Error'), 'Should handle permission errors');
            }
            Test.stopTest();
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationWithTypeBlankRecordType()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            Account secondAccount = new Account(Name = ACCOUNT_SECOND_TEST);
            insert secondAccount;
            
            // Create a contact without record type
            Contact contactNoRT = new Contact(
                FirstName = 'No',
                LastName = 'RecordType',
                AccountId = secondAccount.Id,
                Email = '<EMAIL>'
            );
            insert contactNoRT;
            
            // WHEN: Creating relation with contact that has no/blank record type
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, contactNoRT.Id, 'Staff');
            Test.stopTest();
            
            // THEN: Should handle gracefully
            Assert.isNotNull(result, 'Should return a result');
        }
    }
    
    @isTest
    static void testCreateAccountContactRelationWithTypeDoctorRelationship()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            Account secondAccount = new Account(Name = ACCOUNT_SECOND_TEST);
            insert secondAccount;
            
            // Create a doctor contact
            Id doctorRecordTypeId = getRecordTypeId(DOCTOR_RT_NAME);
            Contact doctorContact = new Contact(
                FirstName = 'Doctor',
                LastName = 'ForRelation',
                AccountId = secondAccount.Id,
                RecordTypeId = doctorRecordTypeId,
                Email = '<EMAIL>'
            );
            insert doctorContact;
            
            // WHEN: Creating doctor relationship using createAccountContactRelationWithType
            Test.startTest();
            String result = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, doctorContact.Id, 'Doctor');
            Test.stopTest();
            
            // THEN: Should create relationship successfully
            Assert.areEqual('Success', result, 'Should create doctor relationship: ' + result);
        }
    }
    
    @isTest
    static void testHelperMethodsCodeCoverage()
    {
        // GIVEN: Current user context
        User currentUser = [SELECT Id, Name, Email, Username, Profile.Name, TimeZoneSidKey, LocaleSidKey 
                           FROM User WHERE Id = :UserInfo.getUserId()];
        
        System.runAs(currentUser)
        {
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            Account secondAccount = new Account(Name = 'Helper Test Account');
            insert secondAccount;
            
            // Create contacts with various record types
            Id doctorRecordTypeId = getRecordTypeId(DOCTOR_RT_NAME);
            Id staffRecordTypeId = getRecordTypeId(STAFF_RT_NAME);
            
            Contact doctorContact = new Contact(
                FirstName = 'Helper',
                LastName = 'Doctor',
                AccountId = secondAccount.Id,
                RecordTypeId = doctorRecordTypeId,
                Email = '<EMAIL>'
            );
            insert doctorContact;
            
            Contact staffContact = new Contact(
                FirstName = 'Helper',
                LastName = 'Staff',
                AccountId = secondAccount.Id,
                RecordTypeId = staffRecordTypeId,
                Email = '<EMAIL>'
            );
            insert staffContact;
            
            // WHEN: Testing various relationship type validations
            Test.startTest();
            
            // Test with JJ_JPN_Staff record type name
            String result1 = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, staffContact.Id, 'Staff');
            
            // Test with Doctor relationship
            String result2 = JJ_JPN_ContactSidebarController.createAccountContactRelationWithType(
                testAccount.Id, doctorContact.Id, 'JJ_JPN_Doctor');
            
            Test.stopTest();
            
            // THEN: Should handle all cases
            Assert.isNotNull(result1, 'Should return result for staff');
            Assert.isNotNull(result2, 'Should return result for doctor');
        }
    }
}