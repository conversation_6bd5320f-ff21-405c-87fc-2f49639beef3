<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <description>Record page for JPN created to default summary tab for some profiles</description>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>CustomButton.Account.JJ_JPN_New_Event</value>
                            <visibilityRule>
                                <booleanFilter>1 AND ( 2 OR 3 OR 4)</booleanFilter>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!$User.Profile.Name}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>ASPAC JPN Sales Rep</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!$User.Profile.Name}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>ASPAC JPN DM</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!$User.Profile.Name}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>ASPAC JPN RM</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.JJ_JPN_NewOrderBtn</value>
                        </valueListItems>
                        <valueListItems>
                            <value>FeedItem.TextPost</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Small</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Global.Task</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Small</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Edit</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.SW_JJ_JPN_EditDelete</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>CustomButton.Account.JJ_JPN_SendContract</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>CustomButton.Account.JJ_JPN_RefreshContractStatus</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>PrintableView</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.SW_Sharing</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Share</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Client.formFactor}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Large</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>6</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Price_Informations__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>JJ_JPN_StoreHospitals__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AccountContactRelations</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Orders</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>JPN_Mystery_Shopping__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Customer_Segmentation_Matrix__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AttachedContentDocuments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>JJ_JPN_Account_CloudSignControls__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer9</identifier>
            </componentInstance>
        </itemInstances>
        <name>relatedTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>force:detailPanel</componentName>
                <identifier>force_detailPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>detailTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>sW_JPN_accountProfilePlaceholder</componentName>
                <identifier>c_sW_JPN_accountProfilePlaceholder</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-5ad3de27-3f7a-411b-912f-ddec0a52f376</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>ShowDebug</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>dynamicApiNames</name>
                    <value>OutletNumber__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>dynamicParamers</name>
                    <value>Calculation_1782299605767471196</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <value>550px</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>objectApiName</name>
                    <value>Account</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>staticParameter</name>
                    <value>&amp;:iframeSizedToWindow=true&amp;:embed=y&amp;:showAppBanner=false&amp;:display_count=no&amp;:showVizHome=no&amp;:refresh=yes&amp;:toolbar=no&amp;</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>url</name>
                    <value>{!$Label.JJ_JPN_Summary_Tableau_Dashboard}</value>
                </componentInstanceProperties>
                <componentName>sW_Clrvw_TableauDashboardCmp</componentName>
                <identifier>c_sW_Clrvw_TableauDashboardCmp</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>ShowDebug</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>dynamicApiNames</name>
                    <value>OutletNumber__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>dynamicParamers</name>
                    <value>Calculation_1856608954208538624</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <value>1200px</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>objectApiName</name>
                    <value>Account</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>staticParameter</name>
                    <value>&amp;:iframeSizedToWindow=true&amp;:embed=y&amp;:showAppBanner=false&amp;:display_count=no&amp;:showVizHome=no&amp;:refresh=yes&amp;:toolbar=no&amp;</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>url</name>
                    <value>{!$Label.JJ_JPN_Summary_Tableau_Dashboard2}</value>
                </componentInstanceProperties>
                <componentName>sW_Clrvw_TableauDashboardCmp</componentName>
                <identifier>c_sW_Clrvw_TableauDashboardCmp2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>ShowDebug</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>dynamicApiNames</name>
                    <value>OutletNumber__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>dynamicParamers</name>
                    <value>Calculation_1782299605767471196</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>height</name>
                    <value>600px</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>objectApiName</name>
                    <value>Account</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>staticParameter</name>
                    <value>&amp;:iframeSizedToWindow=true&amp;:embed=y&amp;:showAppBanner=false&amp;:display_count=no&amp;:showVizHome=no&amp;:refresh=yes&amp;:toolbar=no&amp;</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>url</name>
                    <value>{!$Label.JJ_JPN_Summary_Tableau_Dashboard3}</value>
                </componentInstanceProperties>
                <componentName>sW_Clrvw_TableauDashboardCmp</componentName>
                <identifier>c_sW_Clrvw_TableauDashboardCmp3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.OutletNumber__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>00000</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-0fa437b2-f225-4f10-b500-b46ed1013a2e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>forceChatter:recordFeedContainer</componentName>
                <identifier>forceChatter_recordFeedContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d866cae8-4ec2-4823-b200-70516d9a3d39</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>relatedTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.relatedLists</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>relatedListsTab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>detailTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>detailTab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-5ad3de27-3f7a-411b-912f-ddec0a52f376</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Profile</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0fa437b2-f225-4f10-b500-b46ed1013a2e</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Summary</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d866cae8-4ec2-4823-b200-70516d9a3d39</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Chatter</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <name>maintabs</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>maintabs</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>sW_JPN_drInformation</componentName>
                <identifier>c_sW_JPN_drInformation</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>sW_JPN_staffInformation</componentName>
                <identifier>c_sW_JPN_staffInformation</identifier>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>JPN Summary Account Record Page</masterLabel>
    <sobjectType>Account</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
        <properties>
            <name>actionNames</name>
            <valueList>
                <valueListItems>
                    <value>CustomButton.Account.JJ_JPN_New_Event</value>
                    <visibilityRule>
                        <booleanFilter>1 OR 2 OR 3</booleanFilter>
                        <criteria>
                            <leftValue>{!$User.Profile.Name}</leftValue>
                            <operator>EQUAL</operator>
                            <rightValue>ASPAC JPN Sales Rep</rightValue>
                        </criteria>
                        <criteria>
                            <leftValue>{!$User.Profile.Name}</leftValue>
                            <operator>EQUAL</operator>
                            <rightValue>ASPAC JPN DM</rightValue>
                        </criteria>
                        <criteria>
                            <leftValue>{!$User.Profile.Name}</leftValue>
                            <operator>EQUAL</operator>
                            <rightValue>ASPAC JPN RM</rightValue>
                        </criteria>
                    </visibilityRule>
                </valueListItems>
                <valueListItems>
                    <value>Account.JJ_JPN_NewOrderBtn</value>
                </valueListItems>
                <valueListItems>
                    <value>FeedItem.TextPost</value>
                </valueListItems>
                <valueListItems>
                    <value>Account.Task</value>
                </valueListItems>
                <valueListItems>
                    <value>Edit</value>
                </valueListItems>
                <valueListItems>
                    <value>Account.SW_JJ_JPN_EditDelete</value>
                </valueListItems>
                <valueListItems>
                    <value>CustomButton.Account.JJ_JPN_SendContract</value>
                </valueListItems>
                <valueListItems>
                    <value>CustomButton.Account.JJ_JPN_RefreshContractStatus</value>
                </valueListItems>
                <valueListItems>
                    <value>Account.SW_Sharing</value>
                </valueListItems>
            </valueList>
        </properties>
        <properties>
            <name>enablePageActionConfig</name>
            <value>true</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
