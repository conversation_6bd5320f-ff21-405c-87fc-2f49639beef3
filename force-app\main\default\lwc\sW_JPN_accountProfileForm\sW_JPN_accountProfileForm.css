/* Account Profile Form Styling */

/* Blue strip: SFDC Account Record Page */
.sfdc-account-strip {
    background-color: #e6f7ff;  /* light Salesforce blue */
    color: #003a63;             /* dark text for contrast */
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* No Profile Message Styling */
.slds-illustration {
    background-color: #f3f3f3;
    border-radius: 0.5rem;
    padding: 2rem;
}

/* Edit Mode Alert Styling */
.slds-notify_alert {
    border-radius: 0.25rem;
}

/* Docked Footer for Save/Cancel Buttons */
.slds-docked-form-footer {
    background-color: #f3f3f3;
    border-top: 1px solid #dddbda;
    border-radius: 0 0 0.25rem 0.25rem;
    margin-top: 1rem;
    position: sticky;
    bottom: 0;
    z-index: 1;
}

/* Section Styling */
.slds-section {
    border: 1px solid #dddbda;
    border-radius: 0.25rem;
    background-color: white;
}

.slds-section__title {
    background-color: #f7f9fb;
    border-bottom: 1px solid #dddbda;
    font-weight: 600;
}

/* Field Styling in Edit Mode */
.slds-form-element {
    margin-bottom: 0.75rem;
}

/* Interactive field indicators when NOT in edit mode */
:host(:not(.edit-mode)) lightning-input-field {
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

:host(:not(.edit-mode)) lightning-input-field:hover {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:host(:not(.edit-mode)) lightning-input-field::after {
    content: attr(title);
    position: absolute;
    top: -25px;
    left: 0;
    font-size: 0.75rem;
    color: #706e6b;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    pointer-events: none;
    background: white;
    padding: 2px 6px;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    z-index: 10;
}

:host(:not(.edit-mode)) lightning-input-field:hover::after {
    opacity: 1;
}

/* Highlight editable fields in edit mode */
:host(.edit-mode) lightning-input-field {
    position: relative;
}

:host(.edit-mode) lightning-input-field::before {
    content: '';
    position: absolute;
    top: 0;
    left: -4px;
    width: 4px;
    height: 100%;
    background-color: #1589ee;
    border-radius: 2px;
    opacity: 0.7;
}

/* Button Styling */
.slds-button {
    font-weight: 500;
}

/* Responsive Grid Adjustments */
@media (max-width: 768px) {
    .slds-col {
        flex-basis: 100% !important;
        max-width: 100% !important;
    }
    
    .slds-docked-form-footer .slds-grid {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .slds-docked-form-footer .slds-button {
        width: 100%;
    }
}

/* Loading Spinner Overlay */
.slds-spinner_container {
    position: relative;
    min-height: 200px;
}

/* Success/Error Toast Styling */
.slds-notify {
    border-radius: 0.25rem;
}

/* Field Label Styling */
.slds-form-element__label {
    font-weight: 500;
    color: #3e3e3c;
}

/* Read-only Field Styling */
.slds-form-element_readonly .slds-form-element__static {
    background-color: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 0.25rem;
    padding: 0.5rem;
    min-height: 2.25rem;
    display: flex;
    align-items: center;
}

/* Section Content Padding */
.slds-section__content {
    padding: 1rem;
}

/* Card Header Styling */
.slds-card__header {
    background-color: #f7f9fb;
    border-bottom: 1px solid #dddbda;
}

/* Improved Focus States */
lightning-input-field:focus-within {
    box-shadow: 0 0 0 2px #1589ee;
    border-radius: 0.25rem;
}

/* Edit Mode Indicator */
.edit-mode-indicator {
    position: fixed;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    background-color: #1589ee;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Smooth Transitions */
.slds-section,
.slds-button,
lightning-input-field {
    transition: all 0.2s ease-in-out;
}

/* Hover Effects */
.slds-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Form Validation Styling */
.slds-has-error .slds-form-element__help {
    color: #c23934;
    font-weight: 500;
}

/* Custom Scrollbar for Long Forms */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Field Container with Pencil Button */
.field-container {
    position: relative;
    display: flex;
    align-items: center;
}

.field-container lightning-input-field {
    flex: 1;
}

/* Pencil Button Styling - Always Visible */
.field-edit-button {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    opacity: 1;
    transition: all 0.2s ease-in-out;
    background-color: transparent;
    border: none;
    color: #706e6b;
    padding: 2px;
}

/* Pencil button hover effect */
.field-edit-button:hover {
    color: #0176d3;
    background-color: #f3f3f3;
    border-radius: 0.25rem;
    transform: translateY(-50%) scale(1.1);
}

/* Pencil button focus effect */
.field-edit-button:focus {
    color: #0176d3;
    background-color: #f3f3f3;
    border-radius: 0.25rem;
    box-shadow: 0 0 0 2px #1589ee;
}

/* Adjust field padding to accommodate pencil button */
.field-container lightning-input-field {
    padding-right: 28px;
}

/* Style for readonly fields with standard Salesforce look */
:host(:not(.edit-mode)) .field-container {
    border: 1px solid transparent;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
}

:host(:not(.edit-mode)) .field-container:hover {
    border-color: #dddbda;
    background-color: #fafaf9;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Standard Salesforce field styling */
.field-container {
    min-height: 2.25rem;
    display: flex;
    align-items: center;
    position: relative;
}

/* Hospital Link Styles */
.hospital-link-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: #f3f3f3;
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    min-height: 2.25rem;
    width: 100%;
    box-sizing: border-box;
}

.hospital-link-icon {
    flex-shrink: 0;
    color: #706e6b;
}

.hospital-link {
    color: #0176d3;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 400;
    cursor: pointer;
    transition: color 0.1s ease;
    flex: 1;
}

.hospital-link:hover {
    color: #014486;
    text-decoration: underline;
}

.hospital-link:focus {
    outline: 2px solid #1589ee;
    outline-offset: 2px;
    border-radius: 0.125rem;
}

.hospital-link:visited {
    color: #0176d3;
}

/* Hospital field form element alignment */
.field-container .slds-form-element {
    margin-bottom: 0;
    width: 100%;
}

.field-container .slds-form-element__control {
    width: 100%;
}

/* Ensure hospital field input matches other readonly inputs */
.field-container .slds-form-element__control input[readonly] {
    background-color: #f3f3f3;
    border: 1px solid #d8dde6;
    color: #706e6b;
    min-height: 2.25rem;
    width: 100%;
    box-sizing: border-box;
}