<template>
    <lightning-card title={pageTitle} icon-name="standard:account" class={cardClass}>

        <!-- Loading Spinner -->
        <template if:true={isLoading}>
            <lightning-spinner alternative-text="Loading..." size="medium"></lightning-spinner>
        </template>

        <!-- No Profile Message -->
        <template if:true={showNoProfileMessage}>
            <div class="slds-p-around_medium slds-text-align_center">
                <div class="slds-illustration slds-illustration_small">
                    <div class="slds-m-bottom_medium">
                        <lightning-icon icon-name="utility:info" size="large" variant="inverse"></lightning-icon>
                    </div>
                    <h3 class="slds-text-heading_medium slds-m-bottom_small">
                        {noProfileTitle}
                    </h3>
                    <p class="slds-text-body_regular slds-text-color_weak">
                        {noProfileMessage}
                    </p>
                </div>
            </div>
        </template>

        <!-- Profile Form -->
        <template if:true={profileId}>
            <div class="slds-p-around_medium">

                <!-- Edit Button and Mode Indicator -->
                <div class="slds-grid slds-gutters slds-m-bottom_medium">
                    <div class="slds-col slds-size_1-of-1">
                        <template if:false={isEditMode}>
                            <lightning-button
                                label={editButtonLabel}
                                variant="brand"
                                onclick={handleEdit}
                                icon-name="utility:edit"
                                class="slds-m-right_small">
                            </lightning-button>
                            <p class="slds-text-body_small slds-text-color_weak">
                                {doubleClickHint}
                            </p>
                        </template>
                        <template if:true={isEditMode}>
                            <div class="slds-notify slds-notify_alert slds-theme_info">
                                <span class="slds-assistive-text">Info</span>
                                <lightning-icon icon-name="utility:info" size="x-small" variant="inverse" class="slds-m-right_x-small"></lightning-icon>
                                <h2>{editModeMessage}</h2>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Record Edit Form -->
                <lightning-record-edit-form
                    object-api-name={accountProfileObject}
                    record-id={profileId}
                    onsuccess={handleSuccess}
                    onerror={handleError}
                    form-id="profileForm"
                    readonly={isReadonly}
                    data-edit-mode={isEditMode}>

                    <!-- Hidden fields for contact lookups -->
                    <lightning-input-field field-name="JJ_JPN_Key_Doctor__c" value={drContactId} class="slds-hide"></lightning-input-field>
                    <lightning-input-field field-name="JJ_JPN_Key_Person_1__c" value={keyPerson1ContactId} class="slds-hide"></lightning-input-field>
                    <lightning-input-field field-name="JJ_JPN_Key_Person_2__c" value={keyPerson2ContactId} class="slds-hide"></lightning-input-field>

                    <!-- Blue strip: SFDC Account Record Page -->
                    <div class="sfdc-account-strip slds-m-bottom_small">
                        SFDC Account Record Page
                    </div>

                    <!-- Profile Section -->
                    <div class="slds-section slds-is-open">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">Profile</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-1">
                                    <div class="field-container">
                                        <div class="slds-form-element">
                                            <!-- Hospital Field Label -->
                                            <label class="slds-form-element__label">
                                                {hospitalFieldLabel}
                                            </label>
                                            
                                            <!-- Clickable Hospital Link (when hospital is selected) -->
                                            <template if:true={showHospitalLink}>
                                                <div class="hospital-link-wrapper slds-form-element__control">
                                                    <lightning-icon icon-name="standard:account" size="small" class="hospital-link-icon"></lightning-icon>
                                                    <a 
                                                        href={hospitalRecordUrl}
                                                        onclick={handleHospitalLinkClick}
                                                        class="hospital-link slds-text-link"
                                                        title="View hospital record">
                                                        {relatedHospitalName}
                                                    </a>
                                                </div>
                                            </template>

                                            <!-- Readonly Input (when no hospital is selected) -->
                                            <template if:true={showHospitalReadonlyInput}>
                                                <div class="slds-form-element__control">
                                                    <input
                                                        class="slds-input"
                                                        type="text"
                                                        readonly
                                                        placeholder={hospitalPlaceholder}
                                                        value=""
                                                        style="background-color: #f3f3f3; color: #706e6b;">
                                                </div>
                                            </template>

                                            <!-- Hidden field for form submission -->
                                            <lightning-input-field
                                                field-name="JJ_JPN_Related_Hospital__c"
                                                value={relatedHospitalId}
                                                class="slds-hide">
                                            </lightning-input-field>
                                        </div>
                                        <!-- Hospital field is always read-only - auto-populated from most recent relationship -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Total number of patients per week Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">{totalPatientsTitle}</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Number_1__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Number_1__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Number_2__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Number_2__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- Stakeholder Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small" title="Stakeholder">
                                {stakeholderTitle}
                            </span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <!-- Row 1: Most Influence Dr and Key Person 1 -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <c-s-w_-j-p-n_dynamic-contact
                                            field-name="JJ_JPN_Key_Doctor__c"
                                            field-label={drFieldLabel}
                                            record-id={recordId}
                                            contact-types={drContactTypes}
                                            selected-contact-id={drContactId}
                                            selected-contact-name={drContactName}
                                            is-readonly={isReadonly}
                                            oncontactselect={handleDrContactSelected}>
                                        </c-s-w_-j-p-n_dynamic-contact>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Key_Doctor__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <c-s-w_-j-p-n_dynamic-contact
                                            field-name="JJ_JPN_Key_Person_1__c"
                                            field-label={keyPerson1FieldLabel}
                                            record-id={recordId}
                                            contact-types={keyPersonContactTypes}
                                            selected-contact-id={keyPerson1ContactId}
                                            selected-contact-name={keyPerson1ContactName}
                                            is-readonly={isReadonly}
                                            oncontactselect={handleKeyPerson1ContactSelected}>
                                        </c-s-w_-j-p-n_dynamic-contact>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Key_Person_1__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <!-- Row 2: Number of Orthoptist and Key Person 2 -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Number_3__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Number_3__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <c-s-w_-j-p-n_dynamic-contact
                                            field-name="JJ_JPN_Key_Person_2__c"
                                            field-label={keyPerson2FieldLabel}
                                            record-id={recordId}
                                            contact-types={keyPersonContactTypes}
                                            selected-contact-id={keyPerson2ContactId}
                                            selected-contact-name={keyPerson2ContactName}
                                            is-readonly={isReadonly}
                                            oncontactselect={handleKeyPerson2ContactSelected}>
                                        </c-s-w_-j-p-n_dynamic-contact>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Key_Person_2__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Situation and needs Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small" title="Situation and needs">
                                {situationAndNeedsTitle}
                            </span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <!-- Row 1: Account Growth Rate and Willingness to prescribe CL -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name="JJ_JPN_Picklist_1__c"
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_1__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={willingnessField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={willingnessField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <!-- Row 2: Issue and JJ Sales Issue -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={issueField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={issueField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={jjSalesIssueField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={jjSalesIssueField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <!-- Row 3: Issue Detail and empty -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={issueDetailField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={issueDetailField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <!-- Empty column for Issue Detail row -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Share_ALL Brand Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small" title="Share_ALL Brand">
                                {shareAllBrandTitle}
                            </span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={allJjShareField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={allJjShareField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={allMeniconShareField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={allMeniconShareField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={allAlconShareField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={allAlconShareField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={allSeedShareField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={allSeedShareField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={allCooperShareField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={allCooperShareField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={allOtherShareField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={allOtherShareField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field
                                            field-name={allBlShareField}
                                            readonly={isReadonly}>
                                        </lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field={allBlShareField}>
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Share_DD Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small" title="Share_DD">
                                Share_DD
                            </span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_8__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_8__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_4__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_4__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_9__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_9__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_5__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_5__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_10__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_10__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_6__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_6__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_11__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_11__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_12__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_12__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_13__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_13__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_14__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_14__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Share_RU Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">Share_RU</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_15__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_15__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_7__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_7__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_16__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_16__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_8__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_8__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_17__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_17__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_9__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_9__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_18__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_18__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_19__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_19__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_20__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_20__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_21__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_21__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Share_Ast Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">Share_Ast</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_22__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_22__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_10__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_10__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_23__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_23__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_11__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_11__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_24__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_24__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_12__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_12__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_25__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_25__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_26__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_26__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_27__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_27__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_28__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_28__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Share_MF Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">Share_MF</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_29__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_29__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_13__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_13__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_30__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_30__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_14__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_14__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_31__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_31__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_15__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_15__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_32__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_32__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_33__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_33__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_34__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_34__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_35__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_35__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Share_Beauty Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">Share_Beauty</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_36__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_36__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_16__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_16__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_37__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_37__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_17__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_17__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_38__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_38__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_18__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_18__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_39__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_39__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_40__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_40__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_41__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_41__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2"></div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_42__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_42__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Subscription Ratio Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">{subscriptionRatioTitle}</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <!-- Row 1: Sbsc_Alcon and Sbsc_Menicon -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_43__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_43__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_46__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_46__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <!-- Row 2: Sbsc_Cooper and Sbsc_SEED -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_44__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_44__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_47__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_47__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <!-- Row 3: Sbsc_B&L and Sbsc_Other -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_45__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_45__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Percent_48__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Percent_48__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Phase and Recommend Target Section -->
                    <div class="slds-section slds-is-open slds-m-top_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small">{phaseAndRecommendTitle}</span>
                        </h3>
                        <div class="slds-section__content">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <!-- Row 1: Ast Category_Prescription and MF Category_Prescription -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_19__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_19__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_22__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_22__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <!-- Row 2: Ast Category_Fitting and MF Category_Fitting -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_20__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_20__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_23__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_23__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <!-- Row 3: MAX-A Recommend Target and MAX-MF Recommend Target -->
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_Picklist_21__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_Picklist_21__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-size_1-of-2">
                                    <div class="field-container">
                                        <lightning-input-field field-name="JJ_JPN_MF_Recommend_Target__c" readonly={isReadonly}></lightning-input-field>
                                        <template if:false={isEditMode}>
                                            <lightning-button-icon
                                                icon-name="utility:edit"
                                                variant="bare"
                                                size="small"
                                                alternative-text="Edit this field"
                                                title="Edit this field"
                                                class="field-edit-button"
                                                onclick={handleFieldEdit}
                                                data-field="JJ_JPN_MF_Recommend_Target__c">
                                            </lightning-button-icon>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </lightning-record-edit-form>

                <!-- Save/Cancel Buttons at Bottom -->
                <template if:true={isEditMode}>
                    <div class="slds-docked-form-footer">
                        <div class="slds-grid slds-grid_align-center slds-p-around_medium">
                            <lightning-button
                                label={saveButtonLabel}
                                variant="brand"
                                onclick={handleSave}
                                icon-name="utility:save"
                                class="slds-m-right_small">
                            </lightning-button>
                            <lightning-button
                                label={cancelButtonLabel}
                                variant="neutral"
                                onclick={handleCancel}
                                icon-name="utility:close">
                            </lightning-button>
                        </div>
                    </div>
                </template>
            </div>
        </template>
    </lightning-card>
</template>