import { LightningElement, api, track, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import LANG from '@salesforce/i18n/lang';
import getProfileIdByAccount from '@salesforce/apex/JJ_JPN_AccountProfileController.getProfileIdByAccount';
import getFieldAccessibility from '@salesforce/apex/JJ_JPN_AccountProfileController.getFieldAccessibility';
import getMostRecentRelatedHospital from '@salesforce/apex/JJ_JPN_AccountProfileController.getMostRecentRelatedHospital';
import updateHospitalField from '@salesforce/apex/JJ_JPN_AccountProfileController.updateHospitalField';
import getProfileRecord from '@salesforce/apex/JJ_JPN_AccountProfileController.getProfileRecord';
import getContactById from '@salesforce/apex/JJ_JPN_AccountProfileController.getContactById';
import getAccountById from '@salesforce/apex/JJ_JPN_AccountProfileController.getAccountById';

// Account Profile object and fields
import ACCOUNT_PROFILE_OBJECT from '@salesforce/schema/SW_Account_Profile__c';
import PROFILE_TARGET_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_1__c';
import TEIKIBIN_PHASE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Teikibin_Phase__c';
import TEIKIBIN_TARGET_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Teikibin_Recommend_Target__c';
import MF_PHASE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_MF_Phase__c';
import MF_TARGET_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_MF_Recommend_Target__c';
import AST_PHASE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_AST_Phase__c';
import AST_TARGET_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_AST_Recommend_Target__c';
import TOTAL_PATIENTS_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Number_1__c';
import TOTAL_CL_PATIENTS_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Number_2__c';
import ORTHOPTISTS_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Number_3__c';
import WILLINGNESS_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_2__c';
import ISSUE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_3__c';
import ISSUE_DETAIL_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Text_1__c';
import JJ_SALES_ISSUE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Text_2__c';

// Share fields - ALL Brand
import ALL_JJ_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_1__c';
import ALL_ALCON_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_2__c';
import ALL_COOPER_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_3__c';
import ALL_BL_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_4__c';
import ALL_MENICON_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_5__c';
import ALL_SEED_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_6__c';
import ALL_OTHER_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_7__c';

// Share fields - DD
import DD_JJ_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_8__c';
import DD_ALCON_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_9__c';
import DD_COOPER_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_10__c';
import DD_BL_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_11__c';
import DD_MENICON_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_12__c';
import DD_SEED_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_13__c';
import DD_OTHER_SHARE_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Percent_14__c';

// Major Brand fields (sample - all 15 can be added as needed)
import DD_BRAND_1_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_4__c';
import DD_BRAND_2_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_5__c';

// Contact lookup fields
import KEY_DOCTOR_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Key_Doctor__c';
import KEY_PERSON_1_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Key_Person_1__c';
import KEY_PERSON_2_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Key_Person_2__c';
import DD_BRAND_3_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_6__c';
import RU_BRAND_1_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_7__c';
import RU_BRAND_2_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_8__c';
import RU_BRAND_3_FIELD from '@salesforce/schema/SW_Account_Profile__c.JJ_JPN_Picklist_9__c';

// Constants for timeout delays
// Delay to ensure recordId is available
const RECORD_ID_INIT_DELAY = 100;
// Delay before retrying to add event listeners 
const LISTENER_RETRY_DELAY = 500;
// Delay to ensure DOM is ready for event listeners
const LISTENER_SETUP_DELAY = 1000;
// Delay before resetting listeners after profile load
const LISTENER_RESET_DELAY = 1500;
// Delay used to re-enable edit activation and re-add listeners after cancel
const LISTENER_REENABLE_DELAY_MS = 50;

export default class SWJPNAccountProfileForm extends NavigationMixin(LightningElement) {
    @track profileId = null;
    @track isLoading = true;
    @track isSaving = false;
    @track showNoProfileMessage = false;
    @track fieldAccessibility = {};
    @track isEditMode = false;
    @track listenersAdded = false;
    @track relatedHospitalId = null;
    @track relatedHospitalName = null;

    // Dynamic contact lookup properties
    @track drContactId = null;
    @track drContactName = null;
    @track keyPerson1ContactId = null;
    @track keyPerson1ContactName = null;
    @track keyPerson2ContactId = null;
    @track keyPerson2ContactName = null;

    // Contact types for filtering (comma-separated strings)
    // Note: If these record types don't exist, the component will show all contacts
    drContactTypes = 'JJ_JPN_Doctor';
    keyPersonContactTypes = 'Staff,JJ_JPN_Doctor';

    // Handle recordId changes
    @api
    get recordId() {
        return this._recordId;
    }

    set recordId(value) {
        this._recordId = value;
        if (value && this.isConnected) {
            this.initializeProfile();
        }
    }

    _recordId;
    isConnected = false;

    // temporary suppression flag to avoid re-activating edit mode during resets
    suppressEditActivation = false;

    // Object and field references
    accountProfileObject = ACCOUNT_PROFILE_OBJECT;
    
    // Profile Target & Phases fields
    profileTargetField = PROFILE_TARGET_FIELD;
    teikibinPhaseField = TEIKIBIN_PHASE_FIELD;
    teikibinTargetField = TEIKIBIN_TARGET_FIELD;
    mfPhaseField = MF_PHASE_FIELD;
    mfTargetField = MF_TARGET_FIELD;
    astPhaseField = AST_PHASE_FIELD;
    astTargetField = AST_TARGET_FIELD;
    
    // Totals fields
    totalPatientsField = TOTAL_PATIENTS_FIELD;
    totalClPatientsField = TOTAL_CL_PATIENTS_FIELD;
    orthoptistsField = ORTHOPTISTS_FIELD;
    
    // Issues fields
    willingnessField = WILLINGNESS_FIELD;
    issueField = ISSUE_FIELD;
    issueDetailField = ISSUE_DETAIL_FIELD;
    jjSalesIssueField = JJ_SALES_ISSUE_FIELD;

    // Share fields - ALL Brand
    allJjShareField = ALL_JJ_SHARE_FIELD;
    allAlconShareField = ALL_ALCON_SHARE_FIELD;
    allCooperShareField = ALL_COOPER_SHARE_FIELD;
    allBlShareField = ALL_BL_SHARE_FIELD;
    allMeniconShareField = ALL_MENICON_SHARE_FIELD;
    allSeedShareField = ALL_SEED_SHARE_FIELD;
    allOtherShareField = ALL_OTHER_SHARE_FIELD;

    // Share fields - DD
    ddJjShareField = DD_JJ_SHARE_FIELD;
    ddAlconShareField = DD_ALCON_SHARE_FIELD;
    ddCooperShareField = DD_COOPER_SHARE_FIELD;
    ddBlShareField = DD_BL_SHARE_FIELD;
    ddMeniconShareField = DD_MENICON_SHARE_FIELD;
    ddSeedShareField = DD_SEED_SHARE_FIELD;
    ddOtherShareField = DD_OTHER_SHARE_FIELD;

    // Major Brand fields
    ddBrand1Field = DD_BRAND_1_FIELD;
    ddBrand2Field = DD_BRAND_2_FIELD;

    // Contact lookup fields
    keyDoctorField = KEY_DOCTOR_FIELD;
    keyPerson1Field = KEY_PERSON_1_FIELD;
    keyPerson2Field = KEY_PERSON_2_FIELD;
    ddBrand3Field = DD_BRAND_3_FIELD;
    ruBrand1Field = RU_BRAND_1_FIELD;
    ruBrand2Field = RU_BRAND_2_FIELD;
    ruBrand3Field = RU_BRAND_3_FIELD;

    // Computed properties
    get pageTitle() {
        return LANG !== 'en-US' ? '得意先プロファイル' : 'Account Profile';
    }

    get noProfileTitle() {
        return LANG !== 'en-US' ? 'アカウントプロファイルが見つかりません' : 'No Account Profile Available';
    }

    get noProfileMessage() {
        return LANG !== 'en-US' ?
            'このアカウントにはプロファイル情報が登録されていません。' :
            'No profile information is available for this account.';
    }

    get editModeMessage() {
        return LANG !== 'en-US' ?
            '編集モード: フィールドを変更して下部の保存ボタンをクリックしてください。' :
            'Edit Mode: Modify fields and use Save button at the bottom to save changes.';
    }

    get doubleClickHint() {
        return LANG !== 'en-US' ?
            'ヒント: フィールドをダブルクリックして編集モードを有効にすることもできます。' :
            'Tip: You can also double-click on fields to activate edit mode.';
    }

    get saveButtonLabel() {
        if (this.isSaving) {
            return LANG !== 'en-US' ? '保存中...' : 'Saving...';
        }
        return LANG !== 'en-US' ? '保存' : 'Save';
    }

    get saveButtonIcon() {
        return this.isSaving ? 'utility:spinner' : 'utility:save';
    }

    get cancelButtonLabel() {
        return LANG !== 'en-US' ? 'キャンセル' : 'Cancel';
    }

    get editButtonLabel() {
        return LANG !== 'en-US' ? '編集' : 'Edit';
    }

    get successMessage() {
        return LANG !== 'en-US' ? 'プロファイルが正常に保存されました' : 'Profile saved successfully';
    }

    get errorTitle() {
        return LANG !== 'en-US' ? 'エラー' : 'Error';
    }

    get successTitle() {
        return LANG !== 'en-US' ? '成功' : 'Success';
    }

    get errorSavingProfileMessage() {
        return LANG !== 'en-US' ? 'プロファイルの保存中にエラーが発生しました: ' : 'Error saving profile: ';
    }

    get accountIdRequiredMessage() {
        return LANG !== 'en-US' ? 
            'アカウントIDが必要です。このコンポーネントがアカウントレコードページに配置されていることを確認してください。' : 
            'Account ID is required. Please ensure this component is placed on an Account record page.';
    }

    get initializeProfileErrorMessage() {
        return LANG !== 'en-US' ? 'プロファイルの初期化中にエラーが発生しました: ' : 'Error initializing profile: ';
    }

    get isReadonly() {
        return !this.isEditMode;
    }

    get cardClass() {
        return this.isEditMode ? 'edit-mode' : '';
    }

    // Section titles (localized)
    get totalPatientsTitle() {
        return LANG !== 'en-US' ? '患者数（1週間の合計人数を入力 ・ 半角数字のみ入力可）' : 'Total number of patients per week';
    }

    get situationAndNeedsTitle() {
        return LANG !== 'en-US' ? '施設状況・ニーズ' : 'Situation and needs';
    }

    get shareAllBrandTitle() {
        return LANG !== 'en-US'
            ? 'Share_ALL Brand（半角数字のみ入力可）'
            : 'Share_ALL Brand';
    }

    get subscriptionRatioTitle() {
        return LANG !== 'en-US' ? 'サブスクリプション構成比（各メーカー内のサブスクの構成比を入力）' : 'Subscription Ratio';
    }

    get stakeholderTitle() {
        return LANG !== 'en-US' ? 'ステークホルダー情報（右のDr・スタッフリストを参照にしながら入力）' : 'Stakeholder';
    }

    get phaseAndRecommendTitle() {
        return LANG !== 'en-US' ? 'Speciality施設状況' : 'Phase and Recommend Target';
    }

    get hospitalFieldLabel() {
        return LANG !== 'en-US' ? '処方医療機関名' : 'Related Hospital';
    }

    // Contact field labels - use field labels from object (with translations)
    get drFieldLabel() {
        return this.keyDoctorField.fieldApiName ? this.getFieldLabel(this.keyDoctorField) : 'Dr';
    }

    get keyPerson1FieldLabel() {
        return this.keyPerson1Field.fieldApiName ? this.getFieldLabel(this.keyPerson1Field) : 'Key Person 1';
    }

    get keyPerson2FieldLabel() {
        return this.keyPerson2Field.fieldApiName ? this.getFieldLabel(this.keyPerson2Field) : 'Key Person 2';
    }

    get hospitalPlaceholder() {
        return LANG !== 'en-US' ? '医療機関が選択されていません' : 'No hospital selected';
    }

    // Hospital field computed properties
    get hasSelectedHospital() {
        return this.relatedHospitalId && this.relatedHospitalName;
    }

    get showHospitalLink() {
        return this.isReadonly && this.hasSelectedHospital;
    }

    get showHospitalReadonlyInput() {
        return this.isReadonly && !this.hasSelectedHospital;
    }

    get hospitalRecordUrl() {
        if (!this.relatedHospitalId) {
            return '#';
        }
        return `/lightning/r/Account/${this.relatedHospitalId}/view`;
    }

    // Wire to get field accessibility
    @wire(getFieldAccessibility)
    wiredFieldAccess({ _error, data }) {
        if (data) {
            this.fieldAccessibility = data;
        }
    }

    // Initialize component
    connectedCallback() {
        this.isConnected = true;

        // If recordId is already available, initialize immediately
        if (this.recordId) {
            this.initializeProfile();
        } else {
            // Add a small delay to ensure recordId is available
            setTimeout(() => {
                if (this.recordId) {
                    this.initializeProfile();
                }
            }, RECORD_ID_INIT_DELAY);
        }
        // Add event delegation for automatic edit mode activation
        this.setupFieldEventListeners();
    }

    // Re-add event listeners after render
    renderedCallback() {
        // Only add listeners if profile exists and not already added
        if (this.profileId && !this.listenersAdded) {
            this.setupFieldEventListeners();
        }
    }

    // Setup event listeners for automatic edit mode activation
    setupFieldEventListeners() {
        // Use setTimeout to ensure DOM is ready and profile is loaded
        setTimeout(() => {
            this.addFieldEventListeners();
        }, LISTENER_SETUP_DELAY);
    }

    // Add event listeners to all input fields
    addFieldEventListeners() {
        // Prevent duplicate listeners
        if (this.listenersAdded) return; 

        const inputFields = this.template.querySelectorAll('lightning-input-field');

        if (inputFields.length === 0) {
            setTimeout(() => {
                // Reset flag to retry
                this.listenersAdded = false; 
                this.addFieldEventListeners();
            }, LISTENER_RETRY_DELAY);
            return;
        }

        // Set tooltip text based on language
        const tooltipText = LANG !== 'en-US' ? 'ダブルクリックで編集 ✏️' : 'Double-click to edit ✏️';

        inputFields.forEach(field => {
            // Skip the hospital field - it should remain read-only
            const fieldName = field.getAttribute('field-name');
            if (fieldName === 'JJ_JPN_Related_Hospital__c') {
                // Skip adding event listeners to this field
                return; 
            }

            // Set tooltip attribute for CSS
            field.setAttribute('title', tooltipText);

            // Add double-click listener for text/number fields
            field.addEventListener('dblclick', event => {
                event.preventDefault();
                event.stopPropagation();
                this.activateEditMode('Double-click detected');
            });

            // Add change listener for picklist fields
            field.addEventListener('change', () => {
                this.activateEditMode('Field change detected');
            });
        });
        this.listenersAdded = true;
    }

    // Initialize profile data
    async initializeProfile() {
        try {
            this.isLoading = true;

            if (!this.recordId) {
                this.showError(this.accountIdRequiredMessage);
                return;
            }

            // Check if profile exists and get related hospital in parallel
            const [profileId, hospitalId] = await Promise.all([
                getProfileIdByAccount({ accountId: this.recordId }),
                getMostRecentRelatedHospital({ accountId: this.recordId })
            ]);

            // Set the related hospital ID and load hospital name
            this.relatedHospitalId = hospitalId;
            if (hospitalId) {
                await this.loadHospitalName(hospitalId);
            }

            if (profileId) {
                this.profileId = profileId;

                // Update the hospital field in the database to ensure it's persisted
                try {
                    await updateHospitalField({
                        profileId: profileId,
                        accountId: this.recordId
                    });
                } catch (error) {
                    // Silent failure for non-critical background operation
                }
                
                // Load contact field values from the saved profile
                await this.loadContactFieldValues();
                
                this.showNoProfileMessage = false;

                // Setup event listeners after profile is loaded
                setTimeout(() => {
                    // Reset to ensure listeners are added
                    this.listenersAdded = false;
                    this.setupFieldEventListeners();
                }, LISTENER_RESET_DELAY);
            } else {
                this.showNoProfileMessage = true;
            }

        } catch (error) {
            this.showError(this.initializeProfileErrorMessage + (error.body?.message || error.message));
        } finally {
            this.isLoading = false;
        }
    }

    // Centralized method to activate edit mode
    activateEditMode(_trigger) {
        if (this.suppressEditActivation) {
            return;
        }
        if (!this.isEditMode) {
            this.isEditMode = true;
        }
    }

    // Handle automatic edit mode activation on field interaction
    handleFieldDoubleClick() {
        this.activateEditMode('Field double-click');
    }

    // Handle field change (for picklist and other change events)
    handleFieldChange() {
        this.activateEditMode('Field change');
    }

    // Handle manual edit activation (keeping for backward compatibility)
    handleEdit() {
        this.activateEditMode('Edit button');
    }

    // Handle individual field edit button click (pencil button)
    handleFieldEdit(event) {
        const fieldName = event.target.dataset.field;
        this.activateEditMode(`Pencil button for ${fieldName}`);
    }

    // Handle Dr contact selection
    handleDrContactSelected(event) {
        const { contactId, contactName, isValid } = event.detail;
        
        // Update component state
        this.drContactId = contactId;
        this.drContactName = contactName;
        
        // Update the form field value immediately
        this.updateFormFieldValue('JJ_JPN_Key_Doctor__c', contactId);
        
        // Activate edit mode if not already active and selection is valid
        if (isValid) {
            this.activateEditMode('Dr contact selected');
        }
    }
    
    // Handle Dr contact error
    handleDrContactError(event) {
        const { errorType, message, canRetry } = event.detail;
        this.handleContactFieldError('Dr', errorType, message, canRetry);
    }

    // Handle Key Person 1 contact selection
    handleKeyPerson1ContactSelected(event) {
        const { contactId, contactName, isValid } = event.detail;
        
        // Update component state
        this.keyPerson1ContactId = contactId;
        this.keyPerson1ContactName = contactName;
        
        // Update the form field value immediately
        this.updateFormFieldValue('JJ_JPN_Key_Person_1__c', contactId);
        
        // Activate edit mode if not already active and selection is valid
        if (isValid) {
            this.activateEditMode('Key Person 1 contact selected');
        }
    }
    
    // Handle Key Person 1 contact error
    handleKeyPerson1ContactError(event) {
        const { errorType, message, canRetry } = event.detail;
        this.handleContactFieldError('Key Person 1', errorType, message, canRetry);
    }

    // Handle Key Person 2 contact selection
    handleKeyPerson2ContactSelected(event) {
        const { contactId, contactName, isValid } = event.detail;
        
        // Update component state
        this.keyPerson2ContactId = contactId;
        this.keyPerson2ContactName = contactName;
        
        // Update the form field value immediately
        this.updateFormFieldValue('JJ_JPN_Key_Person_2__c', contactId);
        
        // Activate edit mode if not already active and selection is valid
        if (isValid) {
            this.activateEditMode('Key Person 2 contact selected');
        }
    }
    
    // Handle Key Person 2 contact error
    handleKeyPerson2ContactError(event) {
        const { errorType, message, canRetry } = event.detail;
        this.handleContactFieldError('Key Person 2', errorType, message, canRetry);
    }

    // Helper method to update form field values
    updateFormFieldValue(fieldName, value) {
        const form = this.template.querySelector('lightning-record-edit-form');
        if (form) {
            // Find the specific hidden field by field-name attribute
            const field = this.template.querySelector(`lightning-input-field[field-name="${fieldName}"]`);
            
            if (field) {
                // Set the value property
                field.value = value;
                
                // Force the field to recognize the change by dispatching events
                field.dispatchEvent(new CustomEvent('change', {
                    detail: { value: value },
                    bubbles: true,
                    composed: true
                }));
                
                // Also try to trigger input event for better compatibility
                field.dispatchEvent(new CustomEvent('input', {
                    detail: { value: value },
                    bubbles: true,
                    composed: true
                }));
                
                // Mark the field as dirty to ensure it's included in form submission
                if (field.reportValidity) {
                    field.reportValidity();
                }
            } else {
                // Record diagnostic info silently (no console)
                const allFields = this.template.querySelectorAll('lightning-input-field');
                const available = Array.from(allFields).map(f => ({
                    fieldName: f.fieldName,
                    fieldNameAttr: f.getAttribute('field-name')
                }));
                this._recordDebug?.('updateFormFieldValue.fieldNotFound', fieldName, available);
                this._handleError('updateFormFieldValue.fieldNotFound', new Error(`Field not found: ${fieldName}`));
            }
        } else {
            this._recordDebug?.('updateFormFieldValue.formNotFound');
            this._handleError('updateFormFieldValue.formNotFound', new Error('Form not found'));
        }
    }

    // Handle save button click
    handleSave() {
        // Prevent multiple clicks while saving
        if (this.isSaving) {
            return;
        }

        // Validate all dynamic contact fields before saving
        if (!this.validateContactFields()) {
            return;
        }

        this.isSaving = true;
        
        // Ensure contact field values are set before submission with a small delay
        // to allow the DOM to update
        setTimeout(() => {
            this.updateFormFieldValue('JJ_JPN_Key_Doctor__c', this.drContactId);
            this.updateFormFieldValue('JJ_JPN_Key_Person_1__c', this.keyPerson1ContactId);
            this.updateFormFieldValue('JJ_JPN_Key_Person_2__c', this.keyPerson2ContactId);
            
            // Submit the form after field values are updated
            const form = this.template.querySelector('lightning-record-edit-form');
            if (form) {
                form.submit();

                // Fallback timeout in case form submission gets stuck
                setTimeout(() => {
                    if (this.isSaving) {
                        this.isSaving = false;
                        this.showError('Save operation timed out. Please try again.');
                    }
                }, 10000);
            } else {
                // Reset saving state if form not found
                this.isSaving = false;
                this.showError('Form not found. Please refresh the page and try again.');
            }
        // Small delay to ensure DOM updates    
        }, 100); 
    }
    
    // Validate all dynamic contact fields
    validateContactFields() {
        let isValid = true;
        const contactComponents = this.template.querySelectorAll('c-s-w_-j-p-n_-dynamic-contact');
        
        contactComponents.forEach(component => {
            // Merge nested if statements
            if (component && typeof component.reportValidity === 'function' && !component.reportValidity()) {
                isValid = false;
            }
        });
        
        if (!isValid) {
            this.showError('Please correct the validation errors before saving.');
        }
        
        return isValid;
    }
    
    // Handle contact field errors
    handleContactFieldError(fieldName, errorType, message, _canRetry) {
        const errorInfo = this.getErrorMessageAndVariant(fieldName, errorType, message);
        
        // Only show toast for critical errors that require user attention
        if (errorType === 'permission' || errorType === 'invalid_account') {
            this.showToast('Contact Field Error', errorInfo.userMessage, errorInfo.variant);
        }
    }

    getErrorMessageAndVariant(fieldName, errorType, message) {
        switch (errorType) {
            case 'permission':
                return {
                    userMessage: `Access denied for ${fieldName} contacts. Please contact your administrator.`,
                    variant: 'warning'
                };
            case 'network':
                return {
                    userMessage: `Network error loading ${fieldName} contacts. Please check your connection.`,
                    variant: 'error'
                };
            case 'timeout':
                return {
                    userMessage: `Timeout loading ${fieldName} contacts. Please try again.`,
                    variant: 'warning'
                };
            case 'invalid_account':
                return {
                    userMessage: 'Invalid account context. Please refresh the page.',
                    variant: 'error'
                };
            default:
                return {
                    userMessage: `Error with ${fieldName} field: ${message}`,
                    variant: 'error'
                };
        }
    }

    // Handle form success
    async handleSuccess(event) {
        this.isSaving = false;
        this.isEditMode = false;
        this.showSuccess(this.successMessage);

        await this.updateHospitalFieldAfterSave();
        await this.updateContactFieldsFromEvent(event);

        // Re-add event listeners after form save
        setTimeout(() => {
            this.listenersAdded = false;
            this.setupFieldEventListeners();
        }, LISTENER_RESET_DELAY);
    }

    async updateHospitalFieldAfterSave() {
        // Update hospital field after successful save to ensure it stays current
        if (this.profileId && this.recordId) {
            try {
                await updateHospitalField({
                    profileId: this.profileId,
                    accountId: this.recordId
                });

                // Refresh the hospital ID for display
                const hospitalId = await getMostRecentRelatedHospital({
                    accountId: this.recordId
                });
                this.relatedHospitalId = hospitalId;
                if (hospitalId) {
                    await this.loadHospitalName(hospitalId);
                }
            } catch (error) {
                // Silent failure for non-critical background operation
            }
        }
    }

    async updateContactFieldsFromEvent(event) {
        // Update contact field values from the saved record
        if (event?.detail?.fields) {
            const fields = event.detail.fields;
            
            await this.updateDrContact(fields);
            await this.updateKeyPerson1Contact(fields);
            await this.updateKeyPerson2Contact(fields);
        }
    }

    async updateDrContact(fields) {
        // Update Dr contact
        if (fields.JJ_JPN_Key_Doctor__c) {
            this.drContactId = fields.JJ_JPN_Key_Doctor__c.value;
            if (this.drContactId) {
                await this.loadContactName(this.drContactId, 'dr');
            } else {
                this.drContactName = null;
            }
        }
    }

    async updateKeyPerson1Contact(fields) {
        // Update Key Person 1 contact
        if (fields.JJ_JPN_Key_Person_1__c) {
            this.keyPerson1ContactId = fields.JJ_JPN_Key_Person_1__c.value;
            if (this.keyPerson1ContactId) {
                await this.loadContactName(this.keyPerson1ContactId, 'keyPerson1');
            } else {
                this.keyPerson1ContactName = null;
            }
        }
    }

    async updateKeyPerson2Contact(fields) {
        // Update Key Person 2 contact
        if (fields.JJ_JPN_Key_Person_2__c) {
            this.keyPerson2ContactId = fields.JJ_JPN_Key_Person_2__c.value;
            if (this.keyPerson2ContactId) {
                await this.loadContactName(this.keyPerson2ContactId, 'keyPerson2');
            } else {
                this.keyPerson2ContactName = null;
            }
        }
    }

    // Handle form error
    handleError(event) {
        this.isSaving = false;
        const errorMessage = event.detail?.detail || event.detail?.message || 
            (LANG !== 'en-US' ? 'エラーが発生しました' : 'An error occurred');
        this.showError(this.errorSavingProfileMessage + errorMessage);
    }

    // Handle cancel
    handleCancel(event) {
        this._preventEventDefaults(event);
        this._exitEditMode();
        this._resetFormOrFields();
        this._restoreEventListeners();
    }

    _preventEventDefaults(event) {
        if (!event || typeof event.preventDefault !== 'function') return;
        event.preventDefault();
        event.stopPropagation();
    }

    _exitEditMode() {
        this.isSaving = false;
        this.suppressEditActivation = true;
        this.isEditMode = false;
    }

    _resetFormOrFields() {
        const form = this.template.querySelector('lightning-record-edit-form');
        const canResetForm = form && typeof form.reset === 'function';

        if (canResetForm) {
            try {
                form.reset();
                return; 
            } catch (_ignore) {
                // fall through to per-field reset
            }
        }
        this._resetAllInputFields();
    }

    _resetAllInputFields() {
        const inputFields = this.template.querySelectorAll('lightning-input-field');
        if (!inputFields?.length) return;

        inputFields.forEach(fld => {
            if (!fld || typeof fld.reset !== 'function') return;
            try {
                fld.reset();
            } catch (_ignore) {
                // intentionally ignore individual field errors
            }
        });
    }

    _restoreEventListeners() {
        // Give DOM a short time to settle before re-adding listeners
        setTimeout(() => {
            this.suppressEditActivation = false;
            this.listenersAdded = false;
            this.setupFieldEventListeners();
        }, LISTENER_REENABLE_DELAY_MS);
    }

    // Load contact name by ID
    async loadContactName(contactId, fieldType) {
        try {
            const contact = await getContactById({ contactId: contactId });
            
            if (contact && contact.Name) {
                // Create display name with record type
                let displayName = contact.Name;
                if (contact.RecordType && contact.RecordType.DeveloperName) {
                    const recordTypeName = this.getRecordTypeDisplayName(contact.RecordType.DeveloperName);
                    displayName += ` (${recordTypeName})`;
                }
                
                this.setContactNameByType(fieldType, displayName);
            }
        } catch (error) {
            // Clear the name if we can't load it
            this.setContactNameByType(fieldType, null);
        }
    }

    setContactNameByType(fieldType, displayName) {
        switch (fieldType) {
            case 'dr':
                this.drContactName = displayName;
                break;
            case 'keyPerson1':
                this.keyPerson1ContactName = displayName;
                break;
            case 'keyPerson2':
                this.keyPerson2ContactName = displayName;
                break;
            default:
                // No action for unknown field types
                break;
        }
    }

    // Helper method to get user-friendly record type names
    getRecordTypeDisplayName(developerName) {
        switch (developerName) {
            case 'JJ_JPN_Doctor':
                return 'Dr';
            case 'Staff':
                return 'Staff';
            default:
                // Fallback to developer name
                return developerName; 
        }
    }

    // Load hospital name by ID
    async loadHospitalName(hospitalId) {
        try {
            const hospital = await getAccountById({ accountId: hospitalId });
            
            if (hospital?.Name) {
                this.relatedHospitalName = hospital.Name;
            }
        } catch (error) {
            this.relatedHospitalName = null;
        }
    }

    // Load current contact field values from the profile record
    async loadContactFieldValues() {
        if (!this.profileId) {
            return;
        }

        try {
            const profile = await getProfileRecord({ profileId: this.profileId });
            
            if (profile) {
                // Load Dr contact
                if (profile.JJ_JPN_Key_Doctor__c) {
                    this.drContactId = profile.JJ_JPN_Key_Doctor__c;
                    await this.loadContactName(this.drContactId, 'dr');
                }
                
                // Load Key Person 1 contact
                if (profile.JJ_JPN_Key_Person_1__c) {
                    this.keyPerson1ContactId = profile.JJ_JPN_Key_Person_1__c;
                    await this.loadContactName(this.keyPerson1ContactId, 'keyPerson1');
                }
                
                // Load Key Person 2 contact
                if (profile.JJ_JPN_Key_Person_2__c) {
                    this.keyPerson2ContactId = profile.JJ_JPN_Key_Person_2__c;
                    await this.loadContactName(this.keyPerson2ContactId, 'keyPerson2');
                }
            }
        } catch (error) {
            // Log internally but do not show to user
            this._recordDebug?.('loadContactFieldValues.error');
            this._handleError('loadContactFieldValues', error);
            // Don't show error to user as this is not critical for form functionality
        }
    }

    // Utility methods
    showSuccess(message) {
        this.dispatchEvent(new ShowToastEvent({
            title: this.successTitle,
            message: message,
            variant: 'success'
        }));
    }

    showError(message) {
        this.dispatchEvent(new ShowToastEvent({
            title: this.errorTitle,
            message: message,
            variant: 'error'
        }));
    }
    
    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({
            title: title,
            message: message,
            variant: variant
        }));
    }

    // Check if field is editable
    isFieldEditable(fieldName) {
        const fieldAccess = this.fieldAccessibility[fieldName?.toLowerCase()];
        return fieldAccess?.isUpdateable && fieldAccess?.isAccessible;
    }

    // Check if field is accessible
    isFieldAccessible(fieldName) {
        const fieldAccess = this.fieldAccessibility[fieldName?.toLowerCase()];
        return fieldAccess?.isAccessible;
    }

    // Debug method to manually add event listeners (can be called from console)
    @api
    debugAddEventListeners() {
        this.listenersAdded = false;
        this.addFieldEventListeners();
    }

    // Debug method to test edit mode activation
    @api
    debugActivateEditMode() {
        this.activateEditMode('Debug activation');
    }

    // Handle hospital link click for navigation
    handleHospitalLinkClick(event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (!this.relatedHospitalId) {
            return;
        }

        // Generate URL and open in new tab
        this[NavigationMixin.GenerateUrl]({
            type: 'standard__recordPage',
            attributes: {
                recordId: this.relatedHospitalId,
                objectApiName: 'Account',
                actionName: 'view'
            }
        }).then(url => {
            window.open(url, '_blank');
        });
    }

    // Helper method to get field label (supports translations)
    getFieldLabel(fieldReference) {
        // For now, return the field API name as fallback
        // In a real implementation, you might use getObjectInfo wire service
        // to get proper field labels with translations
        if (fieldReference === KEY_DOCTOR_FIELD) {
            return LANG !== 'en-US' ? 'Dr.名（最もCLに影響度の大きいDr.を入力）' : 'Dr';
        } else if (fieldReference === KEY_PERSON_1_FIELD) {
            return LANG !== 'en-US' ? 'KeyMan-1 名前' : 'Key Person 1';
        } else if (fieldReference === KEY_PERSON_2_FIELD) {
            return LANG !== 'en-US' ? 'KeyMan-2 名前' : 'Key Person 2';
        } else {
            // Default label for unknown fields
            return 'Field Label';
        }
    }

    // Minimal internal error handler - no console calls
    _handleError(context, err, opts = {}) {
        try {
            this._lastError = {
                context,
                message: err?.body?.message || err?.message || String(err),
                raw: err,
                ts: Date.now()
            };

            // Optional toast for user-visible errors
            if (opts.showToast) {
                const userMsg = 'An error occurred';
                this.showError(`${userMsg}: ${this._lastError.message}`);
            }
        } catch (_ignore) {
            // Silently ignore secondary errors
        }
    }

    _recordDebug(..._args) {
    // intentionally left blank for production (enable local debugging if needed)
    }

}