/* Doctor Container with <PERSON><PERSON> - Show max 2, scroll for more */
.doctor-container {
    max-height: 400px; /* Reduced height to show more records */
    overflow-y: auto;
    padding-right: 0.5rem;
}

.doctor-card {
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    padding: 0.75rem; /* Reduced padding */
    background-color: #ffffff;
    margin-bottom: 0.5rem; /* Reduced margin */
}

/* Doctor Header with Name and Menu */
.doctor-header {
    align-items: center;
}

/* Doctor Name Link Styling */
.doctor-name-link {
    color: #0176d3;
    text-decoration: none;
    font-weight: 600;
}

.doctor-name-link:hover {
    text-decoration: underline;
    color: #014486;
}

/* Dropdown Menu Styling */
.custom-dropdown-trigger {
    position: relative;
}

.custom-button-menu {
    border: none;
    background: transparent;
}

/* Table Styling for Better Alignment - More Compact */
.custom-doctor-table {
    width: 100%;
    font-size: 0.8125rem; /* Smaller font */
}

.field-label-cell {
    font-weight: 600;
    color: #3e3e3c;
    width: 40%;
    padding: 0.25rem 0.5rem; /* Reduced padding */
    border-right: 1px solid #d8dde6;
    background-color: #f3f3f3;
    font-size: 0.8125rem; /* Smaller font */
}

.field-value-cell {
    color: #181818;
    padding: 0.25rem 0.5rem; /* Reduced padding */
    word-break: break-word;
    font-size: 0.8125rem; /* Smaller font */
}

/* Influence Links */
.influence-link {
    color: #0176d3;
    text-decoration: none;
}

.influence-link:hover {
    text-decoration: underline;
    color: #014486;
}

/* Search Results */
.search-results {
    max-height: 300px;
    overflow-y: auto;
}

/* Modal styling */
.custom-modal-container {
    max-width: 700px;
}

/* Scrollbar styling */
.doctor-container::-webkit-scrollbar {
    width: 6px;
}

.doctor-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.doctor-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.doctor-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Hover effects */
.doctor-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
}

/* Loading state */
.slds-is-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Custom Search Dropdown Styling */
.slds-combobox_container {
    position: relative;
}

.slds-dropdown {
    max-height: 300px;
    overflow-y: auto;
    z-index: 9999;
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slds-listbox__option:hover {
    background-color: #f3f3f3;
    cursor: pointer;
}

.slds-listbox__option-text_entity {
    font-weight: 600;
    color: #181818;
}

.slds-listbox__option-meta_entity {
    color: #3e3e3c;
    font-size: 0.8125rem;
}

/* Search input styling */
.slds-combobox__input {
    width: 100%;
}

/* Loading spinner in dropdown */
.slds-listbox__item .slds-media_small {
    padding: 0.5rem;
}

/* Scrollbar for search dropdown */
.slds-dropdown::-webkit-scrollbar {
    width: 6px;
}

.slds-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.slds-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.slds-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
    .field-label-cell {
        width: 45%;
        font-size: 0.8125rem;
    }
    
    .field-value-cell {
        font-size: 0.8125rem;
    }
    
    .doctor-container {
        max-height: 500px;
    }
}
/* Fiel
d Alignment Fix */
.slds-form-element__control lightning-record-edit-form {
    display: block;
}

.slds-form-element__control lightning-input-field {
    display: block;
}

/* Ensure consistent height for both input fields */
.slds-form-element__control lightning-input,
.slds-form-element__control lightning-input-field {
    min-height: 2.25rem;
}

/* Align labels consistently */
.slds-form-element__label {
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
    line-height: 1.25;
    color: #3e3e3c;
}

/* Ensure form elements have consistent spacing */
.slds-form .slds-form-element {
    margin-bottom: 0;
}

/* Fix any margin issues with the record edit form */
.slds-form-element__control lightning-record-edit-form lightning-input-field {
    margin-bottom: 0;
}