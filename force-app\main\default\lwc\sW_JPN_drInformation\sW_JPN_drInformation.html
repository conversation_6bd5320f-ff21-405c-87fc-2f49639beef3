<template>
    <lightning-card title={cardTitle} icon-name="standard:contact">
        <!-- Add Relationship Button -->
        <div slot="actions">
            <lightning-button 
                label={addRelationshipLabel} 
                variant="brand" 
                icon-name="utility:link"
                onclick={handleAddRelationship}>
            </lightning-button>
        </div>

        <!-- Doctor Contacts Display with <PERSON>roll -->
        <div class="slds-card__body slds-card__body_inner">
            <template if:true={hasDoctorContacts}>
                <div class="doctor-container">
                    <template for:each={doctorContacts} for:item="doctor">
                        <div key={doctor.Id} class="doctor-card slds-box slds-theme_default slds-m-bottom_small">
                            <!-- Doctor Name as Hyperlink with Edit Dropdown -->
                            <div class="doctor-header slds-m-bottom_small slds-grid slds-grid_align-spread">
                                <div class="slds-col">
                                    <h3 class="slds-text-heading_small">
                                        <a href={doctor.contactUrl} target="_blank" class="doctor-name-link">
                                            {doctor.Name}
                                        </a>
                                    </h3>
                                </div>
                                <div class="slds-col slds-no-flex">
                                    <lightning-button-menu 
                                        alternative-text="Show menu" 
                                        icon-size="x-small"
                                        menu-alignment="right">
                                        <lightning-menu-item 
                                            value="edit" 
                                            label={editLabel}
                                            data-acr-id={doctor.accountContactRelationId}
                                            onclick={handleEditRelationship}>
                                        </lightning-menu-item>
                                    </lightning-button-menu>
                                </div>
                            </div>
                            
                            <!-- Doctor Fields Table -->
                            <table class="slds-table slds-table_bordered slds-table_cell-buffer custom-doctor-table">
                                <tbody>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{occupationLabel}</td>
                                        <td class="field-value-cell">{doctor.Occupation__c}</td>
                                    </tr>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{almaMasterNameLabel}</td>
                                        <td class="field-value-cell">{doctor.JJ_JPN_ULTAlmaMaterName__c}</td>
                                    </tr>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{graduationYearLabel}</td>
                                        <td class="field-value-cell">{doctor.JJ_JPN_ULTGraduationYear__c}</td>
                                    </tr>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{medicalOfficeLabel}</td>
                                        <td class="field-value-cell">{doctor.JJ_JPN_MedicalOffice__c}</td>
                                    </tr>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{specialityLabel}</td>
                                        <td class="field-value-cell">{doctor.JJ_JPN_Dr_Speciality__c}</td>
                                    </tr>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{hobbyLabel}</td>
                                        <td class="field-value-cell">{doctor.JJ_JPN_Dr_Hobby__c}</td>
                                    </tr>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{drInfluenceOnLabel}</td>
                                        <td class="field-value-cell">
                                            <template if:true={doctor.JJ_JPN_Dr_Name_InfluenceOn1__r}>
                                                <a href={doctor.influenceOnUrl} target="_blank" class="influence-link">
                                                    {doctor.JJ_JPN_Dr_Name_InfluenceOn1__r.Name}
                                                </a>
                                            </template>
                                        </td>
                                    </tr>
                                    <tr class="slds-hint-parent">
                                        <td class="field-label-cell">{drInfluenceByLabel}</td>
                                        <td class="field-value-cell">
                                            <template if:true={doctor.JJ_JPN_Dr_Name_InfluenceBy1__r}>
                                                <a href={doctor.influenceByUrl} target="_blank" class="influence-link">
                                                    {doctor.JJ_JPN_Dr_Name_InfluenceBy1__r.Name}
                                                </a>
                                            </template>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </template>
                </div>
            </template>

            <!-- No Doctors Message -->
            <template if:false={hasDoctorContacts}>
                <div class="slds-text-align_center slds-p-around_medium">
                    <p class="slds-text-color_weak">No doctor contacts found for this account.</p>
                </div>
            </template>
        </div>

        <!-- Search Modal -->
        <template if:true={isSearchModalOpen}>
            <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container custom-modal-container">
                    <header class="slds-modal__header">
                        <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" 
                                title="Close" onclick={closeSearchModal}>
                            <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                            <span class="slds-assistive-text">Close</span>
                        </button>
                        <h2 class="slds-text-heading_medium slds-hyphenate">{modalTitle}</h2>
                    </header>
                    
                    <div class="slds-modal__content slds-p-around_medium">
                        <!-- Account Contact Relationship Information Header -->
                        <div class="slds-section slds-is-open slds-m-bottom_medium">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title={modalSectionTitle}>
                                    {modalSectionTitle}
                                </span>
                            </h3>
                        </div>

                        <!-- Account Contact Relation Form - Two Column Layout -->
                        <div class="slds-form slds-form_stacked">
                            <div class="slds-grid slds-gutters slds-wrap">
                                <!-- Account Field -->
                                <div class="slds-col slds-size_1-of-2 slds-max-medium-size_1-of-1">
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label" for="account-field">{accountLabel}</label>
                                        <div class="slds-form-element__control">
                                            <lightning-input 
                                                id="account-field"
                                                type="text" 
                                                value={currentAccountName}
                                                readonly>
                                            </lightning-input>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Field - Standard Lookup -->
                                <div class="slds-col slds-size_1-of-2 slds-max-medium-size_1-of-1">
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label" for="contact-field">{contactLabel}</label>
                                        <div class="slds-form-element__control">
                                            <lightning-record-edit-form object-api-name="AccountContactRelation">
                                                <lightning-input-field 
                                                    field-name="ContactId"
                                                    variant="label-hidden"
                                                    value={selectedContactId}
                                                    onchange={handleContactChange}>
                                                </lightning-input-field>
                                            </lightning-record-edit-form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>



                        <!-- Loading Spinner -->
                        <template if:true={isLoading}>
                            <div class="slds-text-align_center slds-p-around_medium">
                                <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
                            </div>
                        </template>
                    </div>

                    <footer class="slds-modal__footer">
                        <lightning-button 
                            label={cancelLabel} 
                            onclick={closeSearchModal}>
                        </lightning-button>
                        <lightning-button 
                            label={saveAndNewLabel} 
                            variant="neutral"
                            onclick={handleSaveAndNew}
                            disabled={isContactNotSelected}
                            class="slds-m-left_x-small">
                        </lightning-button>
                        <lightning-button 
                            label={saveLabel} 
                            variant="brand"
                            onclick={handleSaveRelationship}
                            disabled={isContactNotSelected}
                            class="slds-m-left_x-small">
                        </lightning-button>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </template>
    </lightning-card>
</template>