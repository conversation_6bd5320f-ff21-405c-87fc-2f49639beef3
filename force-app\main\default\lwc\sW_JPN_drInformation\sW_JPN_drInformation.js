import { LightningElement, api, wire, track } from 'lwc';
import { refreshApex } from '@salesforce/apex';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';
import { NavigationMixin } from 'lightning/navigation';
import LANG from '@salesforce/i18n/lang';
import getDoctorContacts from '@salesforce/apex/JJ_JPN_ContactSidebarController.getDoctorContacts';
import createDoctorAccountContactRelation from '@salesforce/apex/JJ_JPN_ContactSidebarController.createDoctorAccountContactRelation';


// Account fields
import ACCOUNT_NAME_FIELD from '@salesforce/schema/Account.Name';

export default class SWJPNDrInformation extends NavigationMixin(LightningElement) {
    // Account Id from record page
    @api recordId; 
    @track doctorContacts = [];
    @track isSearchModalOpen = false;
    @track isLoading = false;
    @track selectedContactId = null;
    @track selectedContactInfo = null;

    
    wiredDoctorContacts;
    wiredAccount;

    // Wire to get account info
    @wire(getRecord, { recordId: '$recordId', fields: [ACCOUNT_NAME_FIELD] })
    wiredAccountData(result) {
        this.wiredAccount = result;
    }

    // Wire to get doctor contacts
    @wire(getDoctorContacts, { accountId: '$recordId' })
    wiredDoctors(result) {
        this.wiredDoctorContacts = result;
        if (result.data) {
            this.doctorContacts = result.data.map(wrapper => {
                const doctor = wrapper.contact;
                return {
                    ...doctor,
                    accountContactRelationId: wrapper.accountContactRelationId,
                    contactUrl: `/lightning/r/Contact/${doctor.Id}/view`,
                    influenceOnUrl: doctor.JJ_JPN_Dr_Name_InfluenceOn1__c ? `/lightning/r/Contact/${doctor.JJ_JPN_Dr_Name_InfluenceOn1__c}/view` : null,
                    influenceByUrl: doctor.JJ_JPN_Dr_Name_InfluenceBy1__c ? `/lightning/r/Contact/${doctor.JJ_JPN_Dr_Name_InfluenceBy1__c}/view` : null
                };
            });
        } else {
            // Handle error case
            const errorTitle = LANG !== 'en-US' ? 'エラー' : 'Error';
            const errorMsg = LANG !== 'en-US' ? 'ドクター連絡先の読み込みエラー: ' : 'Error loading doctor contacts: ';
            this.showToast(errorTitle, errorMsg + (result.error?.body?.message || ''), 'error');
        }
    }

    // Handle Add Relationship button click
    handleAddRelationship() {
        this.isSearchModalOpen = true;
        this.selectedContactId = null;
        this.selectedContactInfo = null;
    }

    // Handle contact selection from lookup
    handleContactChange(event) {
        this.selectedContactId = event.target.value;
        // Get contact info if needed for display
        if (this.selectedContactId) {
            this.selectedContactInfo = { Id: this.selectedContactId };
        } else {
            this.selectedContactInfo = null;
        }
    }

    // Handle save relationship
    async handleSaveRelationship() {
        const success = await this.saveRelationship();
        if (success) {
            this.closeSearchModal();
        }
    }

    // Close search modal
    closeSearchModal() {
        this.isSearchModalOpen = false;
        this.selectedContactId = null;
        this.selectedContactInfo = null;
    }

    // Translate Apex error messages to Japanese
    translateApexError(errorMsg) {
        if (LANG !== 'en-US') {
            // Map of English error messages to Japanese translations
            const translations = {
                'Contact not found': '連絡先が見つかりません',
                'Only Doctor contacts can be added to this relationship': 'このリレーションにはドクターの連絡先のみ追加できます',
                'Relationship already exists': 'リレーションは既に存在します',
                'Missing required parameters': '必須パラメータが不足しています'
            };
            
            // Check for exact match
            if (translations[errorMsg]) {
                return translations[errorMsg];
            }
            
            // Check for partial matches
            for (const [english, japanese] of Object.entries(translations)) {
                if (errorMsg.includes(english)) {
                    return errorMsg.replace(english, japanese);
                }
            }
        }
        
        return errorMsg;
    }

    // Show toast message
    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant
        });
        this.dispatchEvent(event);
    }

    // Getters for template
    get hasDoctorContacts() {
        return this.doctorContacts && this.doctorContacts.length > 0;
    }

    get canAddMoreDoctors() {
        return this.doctorContacts.length < 2;
    }

    get cannotAddMoreDoctors() {
        return this.doctorContacts.length >= 2;
    }

    get currentAccountName() {
        return getFieldValue(this.wiredAccount.data, ACCOUNT_NAME_FIELD);
    }

    get isContactNotSelected() {
        return !this.selectedContactId;
    }

    // Language-based labels
    get cardTitle() {
        return LANG !== 'en-US' ? 'Dr.情報' : 'Dr Information';
    }

    get addRelationshipLabel() {
        return LANG !== 'en-US' ? 'リレーションを追加' : 'Add Relationship';
    }

    get editLabel() {
        return LANG !== 'en-US' ? '編集' : 'Edit';
    }

    get modalTitle() {
        return LANG !== 'en-US' ? '新規取引先と取引先責任者のリレーション' : 'New Account Contact Relationship';
    }

    get modalSectionTitle() {
        return LANG !== 'en-US' ? '得意先と取引先個人のリレーション情報' : 'Account Contact Relationship Information';
    }

    get accountLabel() {
        return LANG !== 'en-US' ? '得意先' : 'Account';
    }

    get contactLabel() {
        return LANG !== 'en-US' ? '取引先個人' : 'Contact';
    }

    get contactPlaceholder() {
        return LANG !== 'en-US' ? 'コンタクトをフィルタリングするために入力...' : 'Type to filter contacts...';
    }

    get searchHelpText() {
        return LANG !== 'en-US' ? '名前、役職、メール、または電話で検索' : 'Search by Name, Occupation, Email, or Phone';
    }

    get availableContactsTitle() {
        return LANG !== 'en-US' ? '利用可能なドクターコンタクト' : 'Available Doctor Contacts';
    }

    get filteredByText() {
        return LANG !== 'en-US' ? 'でフィルタリング' : 'Filtered by';
    }

    get selectLabel() {
        return LANG !== 'en-US' ? '選択' : 'Select';
    }

    get noContactsMessage() {
        return LANG !== 'en-US' ? '利用可能なドクターコンタクトが見つかりません' : 'No available doctor contacts found';
    }

    get cancelLabel() {
        return LANG !== 'en-US' ? 'キャンセル' : 'Cancel';
    }

    get saveAndNewLabel() {
        return LANG !== 'en-US' ? '保存＆新規' : 'Save & New';
    }

    get saveLabel() {
        return LANG !== 'en-US' ? '保存' : 'Save';
    }

    // Field labels
    get occupationLabel() {
        return LANG !== 'en-US' ? '役職' : 'Occupation';
    }

    get almaMasterNameLabel() {
        return LANG !== 'en-US' ? '出身大学' : 'ULT AlmaMasterName';
    }

    get graduationYearLabel() {
        return LANG !== 'en-US' ? '卒業年' : 'ULT Graduation Year';
    }

    get medicalOfficeLabel() {
        return LANG !== 'en-US' ? '出身/所属中の医局' : 'Medical Office';
    }

    get specialityLabel() {
        return LANG !== 'en-US' ? '専門' : 'Speciality';
    }

    get hobbyLabel() {
        return LANG !== 'en-US' ? '趣味' : 'Hobby';
    }

    get drInfluenceOnLabel() {
        return LANG !== 'en-US' ? '影響を受けるDr' : 'Dr Influence on 1';
    }

    get drInfluenceByLabel() {
        return LANG !== 'en-US' ? '影響を与えるDr' : 'Dr Influence by 1';
    }

    get lookupHelpText() {
        return LANG !== 'en-US' ? 'ドクターを検索して選択してください' : 'Search and select a doctor contact';
    }

    // Handle save and new
    async handleSaveAndNew() {
        const success = await this.saveRelationship();
        if (success) {
            // Reset form for new entry
            this.selectedContactId = null;
            this.selectedContactInfo = null;
        }
    }

    // Common save logic - refactored to reduce complexity
    async saveRelationship() {
        if (!this.validateContactSelection()) {
            return false;
        }

        this.isLoading = true;
        try {
            return await this.createDoctorRelationship();
        } catch (error) {
            this.handleRelationshipError(error);
            return false;
        } finally {
            this.isLoading = false;
        }
    }

    validateContactSelection() {
        if (!this.selectedContactId) {
            const warningTitle = LANG !== 'en-US' ? '警告' : 'Warning';
            const warningMsg = LANG !== 'en-US' ? '最初に連絡先を選択してください' : 'Please select a contact first';
            this.showToast(warningTitle, warningMsg, 'warning');
            return false;
        }
        return true;
    }

    async createDoctorRelationship() {
        const result = await createDoctorAccountContactRelation({
            contactId: this.selectedContactId,
            accountId: this.recordId
        });

        if (result === 'Success') {
            const successTitle = LANG !== 'en-US' ? '成功' : 'Success';
            const successMsg = LANG !== 'en-US' ? 'ドクターのリレーションが正常に追加されました' : 'Doctor relationship added successfully';
            this.showToast(successTitle, successMsg, 'success');
            await refreshApex(this.wiredDoctorContacts);
            return true;
        }
        
        const warningTitle = LANG !== 'en-US' ? '警告' : 'Warning';
        const translatedMsg = this.translateApexError(result);
        this.showToast(warningTitle, translatedMsg, 'warning');
        return false;
    }

    handleRelationshipError(error) {
        const errorTitle = LANG !== 'en-US' ? 'エラー' : 'Error';
        const errorMsg = LANG !== 'en-US' ? 'リレーション作成エラー: ' : 'Error creating relationship: ';
        this.showToast(errorTitle, errorMsg + error.body.message, 'error');
    }

    // Handle edit AccountContactRelation
    handleEditRelationship(event) {
        const acrId = event.target.dataset.acrId;
        if (acrId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: {
                    recordId: acrId,
                    objectApiName: 'AccountContactRelation',
                    actionName: 'edit'
                }
            });
        } else {
            const errorTitle = LANG !== 'en-US' ? 'エラー' : 'Error';
            const errorMsg = LANG !== 'en-US' ? '編集するリレーションレコードが見つかりません' : 'Unable to find relationship record to edit';
            this.showToast(errorTitle, errorMsg, 'error');
        }
    }
}