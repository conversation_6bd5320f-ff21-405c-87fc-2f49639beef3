/* Dynamic Contact Lookup Component Styles - Modal Based */

.dynamic-contact-lookup {
    position: relative;
    width: 100%;
}

.dynamic-contact-lookup .slds-form-element {
    position: relative;
}

.dynamic-contact-lookup .slds-form-element__control {
    position: relative;
}

/* Form element styling */
.dynamic-contact-lookup .slds-form-element__label {
    font-weight: 400;
    font-size: 0.75rem;
    line-height: 1.25;
    color: #444;
    margin-bottom: 0.125rem;
}

.dynamic-contact-lookup .slds-required {
    color: #c23934;
    margin-left: 0.125rem;
}

/* Lookup Input Wrapper */
.lookup-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

/* Lookup field styling to match standard Salesforce lookup appearance */
.dynamic-contact-lookup .slds-input {
    cursor: pointer;
    background-color: white;
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    padding: 0 2.5rem 0 0.75rem;
    height: 2.25rem;
    line-height: 2.25rem;
    font-size: 0.875rem;
    color: #181818;
    flex: 1;
    width: 100%;
}

.dynamic-contact-lookup .slds-input:focus {
    outline: none;
    border-color: #1589ee;
    box-shadow: 0 0 3px #0176d3;
}

.dynamic-contact-lookup .slds-input:hover:not([readonly]) {
    border-color: #1589ee;
}

.dynamic-contact-lookup .slds-input_readonly {
    cursor: default;
    background-color: #f3f3f3;
    border-color: #d8dde6;
    color: #706e6b;
}

.dynamic-contact-lookup .slds-input_readonly:hover {
    border-color: #d8dde6;
}

/* Icon button styling */
.lookup-icon-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #706e6b;
}

.lookup-icon-button:hover {
    background-color: #f3f3f3;
    color: #181818;
}

.lookup-icon-button:focus {
    outline: 2px solid #1589ee;
    outline-offset: -2px;
}

/* Dropdown Wrapper - Shows max 4 items with scrollbar */
.dropdown-wrapper {
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.16);
    z-index: 9999;
    min-width: 100%;
}

/* Dropdown List - Max height for 4 items */
.dropdown-list {
    display: flex;
    flex-direction: column;
    gap: 0;
    max-height: calc(2.5rem * 4);
    overflow-y: auto;
}

/* Dropdown States */
.dropdown-loading,
.dropdown-error,
.dropdown-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    text-align: center;
    min-height: 100px;
}

.dropdown-loading p,
.dropdown-error p,
.dropdown-empty p {
    margin: 0.5rem 0 0 0;
    color: #706e6b;
    font-size: 0.875rem;
}

.dropdown-error {
    color: #ea001e;
}

.dropdown-error p {
    color: #ea001e;
}

/* Scrollbar styling for dropdown */
.dropdown-list::-webkit-scrollbar {
    width: 8px;
}

.dropdown-list::-webkit-scrollbar-track {
    background: transparent;
}

.dropdown-list::-webkit-scrollbar-thumb {
    background: #d8dde6;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
    background: #c9ccd4;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
}

/* Modal Container */
.modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 0.25rem;
    box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.25);
    z-index: 9999;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #dddbda;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #181818;
}

.modal-close-button {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #706e6b;
}

.modal-close-button:hover {
    background-color: #f3f3f3;
    color: #181818;
}

/* Modal Body */
.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* Search Container */
.search-container {
    position: relative;
    margin-bottom: 1rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    box-sizing: border-box;
}

.search-input:focus {
    outline: none;
    border-color: #1589ee;
    box-shadow: 0 0 3px #0176d3;
}

.search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #706e6b;
    pointer-events: none;
}

/* Contacts List */
.contacts-list {
    display: flex;
    flex-direction: column;
    gap: 0;
}

/* Contact Item */
.contact-item-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid #f3f3f3;
    cursor: pointer;
    transition: background-color 0.1s ease;
    min-height: 2.5rem;
}

.contact-item:hover {
    background-color: #f3f3f3;
}

.contact-item:focus {
    background-color: #f3f3f3;
    outline: 2px solid #1589ee;
    outline-offset: -2px;
}

.contact-item.selected {
    background-color: #e8f4fd;
}

.contact-item.selected:hover {
    background-color: #d8edff;
}

/* Dropdown contact item styling */
.dropdown-list .contact-item {
    min-height: 2.5rem;
    padding: 0.5rem 0.75rem;
}

.contact-icon {
    flex-shrink: 0;
    color: #706e6b;
}

.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-name {
    font-weight: 600;
    color: #181818;
    font-size: 0.875rem;
}

.contact-record-type {
    font-weight: 400;
    color: #706e6b;
    margin-left: 0.25rem;
}

.contact-title {
    color: #706e6b;
    font-size: 0.75rem;
    margin-top: 0.125rem;
}

.contact-check {
    flex-shrink: 0;
    color: #0176d3;
}

/* Modal Footer */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid #dddbda;
    background-color: #f7f9fb;
}

/* Modal States */
.modal-loading,
.modal-error,
.modal-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    min-height: 200px;
}

.modal-loading p,
.modal-error p,
.modal-empty p {
    margin: 1rem 0 0 0;
    color: #706e6b;
    font-size: 0.875rem;
}

.modal-error {
    color: #ea001e;
}

.modal-error p {
    color: #ea001e;
}

/* Error state styling */
.dynamic-contact-lookup .slds-has-error .slds-input {
    border-color: #ea001e;
    box-shadow: 0 0 3px #ea001e;
}

.dynamic-contact-lookup .slds-has-error .slds-form-element__label {
    color: #ea001e;
}

/* Scrollbar styling for modal body */
.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f3f3f3;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #d8dde6;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #c9ccd4;
}

/* Contact Link Styles */
.contact-link-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: #f3f3f3;
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    min-height: 2.25rem;
}

.contact-link-icon {
    flex-shrink: 0;
    color: #706e6b;
}

.contact-link {
    color: #0176d3;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 400;
    cursor: pointer;
    transition: color 0.1s ease;
}

.contact-link:hover {
    color: #014486;
    text-decoration: underline;
}

.contact-link:focus {
    outline: 2px solid #1589ee;
    outline-offset: 2px;
    border-radius: 0.125rem;
}

.contact-link:visited {
    color: #0176d3;
}

/* Readonly Input Wrapper */
.readonly-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

/* Accessibility improvements */
.contact-item:focus-visible {
    outline: 2px solid #1589ee;
    outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .contact-item:hover {
        background-color: #000;
        color: #fff;
    }

    .contact-item:focus {
        background-color: #000;
        color: #fff;
        outline: 2px solid #fff;
    }
}

/* Responsive behavior for mobile */
@media (max-width: 768px) {
    .modal-container {
        width: 95%;
        max-height: 90vh;
    }

    .modal-header {
        padding: 0.75rem;
    }

    .modal-title {
        font-size: 1rem;
    }

    .modal-body {
        padding: 0.75rem;
    }

    .modal-footer {
        padding: 0.75rem;
    }

    .contact-item {
        padding: 0.5rem;
    }

    .contact-name {
        font-size: 0.8125rem;
    }

    .contact-title {
        font-size: 0.6875rem;
    }
}