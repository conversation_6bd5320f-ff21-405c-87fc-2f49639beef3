<template>
    <div class="dynamic-contact-lookup">
        <!-- Lookup Field Container -->
        <div class={formElementClass}>
            <label class="slds-form-element__label" for={uniqueId}>
                <span class="slds-required" if:true={required}>*</span>
                {fieldLabel}
            </label>
            <div class="slds-form-element__control">
                <!-- Clickable Contact Link (readonly mode with selected contact) -->
                <template if:true={showContactLink}>
                    <div class="contact-link-wrapper">
                        <lightning-icon icon-name="standard:contact" size="small" class="contact-link-icon"></lightning-icon>
                        <a 
                            href={contactRecordUrl}
                            onclick={handleContactLinkClick}
                            class="contact-link slds-text-link"
                            title="View contact record">
                            {selectedContactName}
                        </a>
                    </div>
                </template>

                <!-- Readonly Input (readonly mode without selected contact) -->
                <template if:true={showReadonlyInput}>
                    <div class="readonly-input-wrapper">
                        <input
                            id={uniqueId}
                            class={inputClass}
                            type="text"
                            placeholder={computedPlaceholder}
                            readonly
                            value={selectedContactName}
                            aria-describedby={helpTextId}
                            aria-required={required}
                            role="textbox" />
                    </div>
                </template>

                <!-- Editable Lookup Input (edit mode) -->
                <template if:true={showEditableInput}>
                    <div class="lookup-input-wrapper">
                        <input
                            id={uniqueId}
                            class={inputClass}
                            type="text"
                            placeholder={computedPlaceholder}
                            readonly={isReadonly}
                            value={selectedContactName}
                            onclick={handleLookupClick}
                            onkeydown={handleKeyDown}
                            onfocus={handleInputFocus}
                            onblur={handleInputBlur}
                            aria-describedby={helpTextId}
                            aria-required={required}
                            role="textbox" />

                        <!-- Clear Button (when contact is selected and not readonly) -->
                        <template if:true={showClearButton}>
                            <button
                                class="lookup-icon-button lookup-clear-button"
                                type="button"
                                title="Clear selection"
                                onclick={handleClearSelection}
                                aria-label="Clear selection">
                                <lightning-icon icon-name="utility:clear" size="x-small" alternative-text="Clear"></lightning-icon>
                            </button>
                        </template>

                        <!-- Loading Spinner -->
                        <template if:true={isLoading}>
                            <div class="lookup-icon-button">
                                <lightning-spinner size="x-small" alternative-text="Loading contacts"></lightning-spinner>
                            </div>
                        </template>

                        <!-- Lookup Icon -->
                        <template if:true={showLookupIcon}>
                            <button
                                class="lookup-icon-button lookup-search-button"
                                type="button"
                                title="Open contact selector"
                                onclick={handleLookupClick}
                                tabindex="-1"
                                aria-label="Open contact selector">
                                <lightning-icon
                                    icon-name="utility:search"
                                    size="x-small"
                                    alternative-text="Open contact selector">
                                </lightning-icon>
                            </button>
                        </template>
                    </div>
                </template>
            </div>

            <!-- Validation Error Message -->
            <template if:true={showErrorMessage}>
                <div id={helpTextId} class="slds-form-element__help slds-has-error">{displayErrorMessage}</div>
            </template>

            <!-- Help Text -->
            <template if:true={helpText}>
                <template if:false={showErrorMessage}>
                    <div id={helpTextId} class="slds-form-element__help">{helpText}</div>
                </template>
            </template>
        </div>

        <!-- Dropdown Container -->
        <template if:true={isDropdownOpen}>
            <div class="dropdown-wrapper">
                <!-- Loading State -->
                <template if:true={isLoading}>
                    <div class="dropdown-loading">
                        <lightning-spinner size="small" alternative-text="Loading contacts"></lightning-spinner>
                        <p>Loading contacts...</p>
                    </div>
                </template>

                <!-- Error State -->
                <template if:true={hasError}>
                    <div class="dropdown-error">
                        <lightning-icon icon-name="utility:error" size="medium" variant="error"></lightning-icon>
                        <p>{errorMessage}</p>
                        <template if:true={showRetry}>
                            <lightning-button-icon 
                                icon-name="utility:refresh" 
                                onclick={handleRetry} 
                                title="Retry"
                                alternative-text="Retry loading contacts">
                            </lightning-button-icon>
                        </template>
                    </div>
                </template>

                <!-- Empty State -->
                <template if:true={showEmptyState}>
                    <div class="dropdown-empty">
                        <lightning-icon icon-name="utility:info" size="medium"></lightning-icon>
                        <p>{emptyStateMessage}</p>
                    </div>
                </template>

                <!-- Contact List -->
                <template if:false={isLoading}>
                    <template if:false={hasError}>
                        <div class="dropdown-list">
                            <template for:each={contacts} for:item="contact">
                                <div
                                    key={contact.Id}
                                    data-contact-id={contact.Id}
                                    class="contact-item"
                                    onclick={handleContactSelect}
                                    onkeydown={handleContactKeyDown}
                                    role="option"
                                    tabindex="0">
                                    <div class="contact-item-content">
                                        <lightning-icon icon-name="standard:contact" size="small" class="contact-icon"></lightning-icon>
                                        <div class="contact-info">
                                            <div class="contact-name">
                                                {contact.Name}
                                                <template if:true={contact.recordTypeDisplay}>
                                                    <span class="contact-record-type">({contact.recordTypeDisplay})</span>
                                                </template>
                                            </div>
                                            <template if:true={contact.Title}>
                                                <div class="contact-title">{contact.Title}</div>
                                            </template>
                                        </div>
                                    </div>
                                    <template if:true={contact.isSelected}>
                                        <lightning-icon icon-name="utility:check" size="small" class="contact-check"></lightning-icon>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </template>
                </template>
            </div>
        </template>
    </div>
</template>