import { LightningElement, api, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import getFilteredContacts from '@salesforce/apex/JJ_JPN_AccountProfileController.getFilteredContacts';

// Constants for component behavior
// Cache duration: 5 minutes in milliseconds
const MINUTES_TO_MS = 60 * 1000;
const CACHE_DURATION_MINUTES = 5;
const CACHE_DURATION_MS = CACHE_DURATION_MINUTES * MINUTES_TO_MS;
// Debounce delay: 300ms
const DEBOUNCE_DELAY_MS = 300;
// Maximum contacts to display
const MAX_CONTACTS_DISPLAY = 200;
// Random string generation constants
const RANDOM_STRING_RADIX = 36;
const RANDOM_STRING_LENGTH = 9;
// Retry delay constants
const RETRY_BASE_DELAY_MS = 1000;
const RETRY_MAX_DELAY_MS = 5000;
const RETRY_BACKOFF_EXPONENT = 2;

export default class SWJPNDynamicContact extends NavigationMixin(LightningElement) {
    // Public API properties
    @api fieldName = '';
    @api fieldLabel = '';
    @api recordId = '';
    @api contactTypes = '';
    
    // Convert comma-separated string to array
    get contactTypesArray() {
        return this.contactTypes ? this.contactTypes.split(',').map(type => type.trim()) : [];
    }
    @api selectedContactId = '';
    @api selectedContactName = '';
    @api isReadonly = false;
    @api required = false;
    @api placeholder = 'Select a contact...';
    @api helpText = '';

    // Private tracked properties
    @track contacts = [];
    @track isDropdownOpen = false;
    @track isLoading = false;
    @track hasError = false;
    @track errorMessage = '';
    @track showRetry = false;
    @track hasValidationError = false;
    @track validationErrorMessage = '';
    @track networkError = false;
    @track permissionError = false;

    // Private properties
    _contactsCache = null;
    _cacheTimestamp = null;
    _debounceTimeout = null;
    _isConnected = false;
    _lastAccountId = null;
    _lastContactTypes = null;

    // Computed properties
    get uniqueId() {
        const randomPart = Math.random().toString(RANDOM_STRING_RADIX).substring(2, 2 + RANDOM_STRING_LENGTH);
        return `dynamic-contact-${this.fieldName}-${randomPart}`;
    }

    get helpTextId() {
        return `${this.uniqueId}-help`;
    }

    get formElementClass() {
        let baseClass = 'slds-form-element';
        if (this.hasError || this.hasValidationError) {
            baseClass += ' slds-has-error';
        }
        return baseClass;
    }
    
    get displayErrorMessage() {
        if (this.hasValidationError) {
            return this.validationErrorMessage;
        }
        if (this.hasError) {
            return this.errorMessage;
        }
        return '';
    }
    
    get showErrorMessage() {
        return this.hasError || this.hasValidationError;
    }

    get inputClass() {
        let baseClass = 'slds-input slds-combobox__input';
        if (this.isReadonly) {
            baseClass += ' slds-input_readonly';
        }
        return baseClass;
    }

    get computedPlaceholder() {
        if (this.isReadonly) {
            return '';
        }
        return this.selectedContactName ? '' : this.placeholder;
    }

    get showClearButton() {
        return !this.isReadonly && !this.isLoading && this.selectedContactId && this.selectedContactName;
    }

    get showLookupIcon() {
        return !this.isLoading && !this.showClearButton;
    }

    get filteredContacts() {
        if (!this.searchTerm || this.searchTerm.trim() === '') {
            return this.contacts;
        }

        const searchLower = this.searchTerm.toLowerCase();
        return this.contacts.filter(contact => {
            const nameMatch = (contact.Name || '').toLowerCase().includes(searchLower);
            const titleMatch = (contact.Title || '').toLowerCase().includes(searchLower);
            return nameMatch || titleMatch;
        });
    }

    get showEmptyState() {
        return !this.isLoading && !this.hasError && this.contacts.length === 0;
    }

    get emptyStateMessage() {
        if (!this.recordId) {
            return 'No account selected';
        }
        if (!this.contactTypes || this.contactTypes.length === 0) {
            return 'No contact types specified';
        }
        
        // Create a user-friendly message based on contact types
        const typeNames = this.contactTypesArray.map(type => {
            switch (type) {
                case 'JJ_JPN_Doctor':
                    return 'Doctor';
                case 'Staff':
                    return 'Staff';
                default:
                    return type;
            }
        });
        
        if (typeNames.length === 1) {
            return `No ${typeNames[0]} contacts found for this account`;
        } else {
            return `No ${typeNames.join(' or ')} contacts found for this account`;
        }
    }

    // Properties for contact link functionality
    get hasSelectedContact() {
        return this.selectedContactId && this.selectedContactName;
    }

    get showContactLink() {
        return this.isReadonly && this.hasSelectedContact;
    }

    get showReadonlyInput() {
        return this.isReadonly && !this.hasSelectedContact;
    }

    get showEditableInput() {
        return !this.isReadonly;
    }

    get contactRecordUrl() {
        if (!this.selectedContactId) {
            return '#';
        }
        return `/lightning/r/Contact/${this.selectedContactId}/view`;
    }

    // Lifecycle methods
    connectedCallback() {
        this._isConnected = true;
        this.setupEventListeners();
    }

    disconnectedCallback() {
        this._isConnected = false;
        this.cleanupEventListeners();
        this.clearDebounceTimeout();
    }

    renderedCallback() {
        // Ensure event listeners are properly set up after render
        if (this._isConnected) {
            this.setupEventListeners();
        }
    }

    // Event listener setup
    setupEventListeners() {
        // Add click outside listener to close dropdown
        if (!this._clickOutsideHandler) {
            this._clickOutsideHandler = this.handleClickOutside.bind(this);
            document.addEventListener('click', this._clickOutsideHandler);
        }

        // Add window resize listener to reposition dropdown
        if (!this._resizeHandler) {
            this._resizeHandler = this.handleWindowResize.bind(this);
            window.addEventListener('resize', this._resizeHandler);
        }

        // Add scroll listener to reposition dropdown
        if (!this._scrollHandler) {
            this._scrollHandler = this.handleWindowScroll.bind(this);
            window.addEventListener('scroll', this._scrollHandler, true);
        }
    }

    cleanupEventListeners() {
        if (this._clickOutsideHandler) {
            document.removeEventListener('click', this._clickOutsideHandler);
            this._clickOutsideHandler = null;
        }

        if (this._resizeHandler) {
            window.removeEventListener('resize', this._resizeHandler);
            this._resizeHandler = null;
        }

        if (this._scrollHandler) {
            window.removeEventListener('scroll', this._scrollHandler, true);
            this._scrollHandler = null;
        }
    }

    handleWindowResize() {
        if (this.isDropdownOpen) {
            // Debounce resize handling
            clearTimeout(this._resizeTimeout);
            this._resizeTimeout = setTimeout(() => {
                this.positionDropdown();
            }, 100);
        }
    }

    handleWindowScroll() {
        if (this.isDropdownOpen) {
            // Close dropdown on scroll to prevent positioning issues
            this.closeDropdown();
        }
    }

    // Public API methods
    @api
    clearSelection() {
        this.selectedContactId = '';
        this.selectedContactName = '';
        this.closeDropdown();
        this.dispatchSelectionChange();
    }

    @api
    refreshContacts() {
        this.invalidateCache();
        if (this.isDropdownOpen) {
            this.loadContacts();
        }
    }

    @api
    validate() {
        // Clear any existing validation errors
        this.clearValidationError();
        
        // Check if field is required and has no value
        if (this.required && !this.selectedContactId) {
            const errorMessage = `${this.fieldLabel} is required.`;
            this.setValidationError(errorMessage);
            return {
                isValid: false,
                errorMessage: errorMessage
            };
        }
        
        // Check if account context is missing
        if (!this.recordId) {
            const errorMessage = `Account context is required for ${this.fieldLabel}.`;
            this.setValidationError(errorMessage);
            return {
                isValid: false,
                errorMessage: errorMessage
            };
        }
        
        // Check if contact types are configured
        if (!this.contactTypes || this.contactTypesArray.length === 0) {
            const errorMessage = `Contact types not configured for ${this.fieldLabel}.`;
            this.setValidationError(errorMessage);
            return {
                isValid: false,
                errorMessage: errorMessage
            };
        }
        
        // Check if selected contact is valid (exists in current contact list)
        if (this.selectedContactId && this.contacts.length > 0) {
            const selectedContact = this.contacts.find(contact => contact.Id === this.selectedContactId);
            if (!selectedContact) {
                const errorMessage = `Selected contact is no longer available for ${this.fieldLabel}.`;
                this.setValidationError(errorMessage);
                return {
                    isValid: false,
                    errorMessage: errorMessage
                };
            }
        }
        
        return {
            isValid: true,
            errorMessage: null
        };
    }

    @api
    reportValidity() {
        const validation = this.validate();
        if (!validation.isValid) {
            this.showToast('Validation Error', validation.errorMessage, 'error');
            this.focusInput();
        }
        return validation.isValid;
    }
    
    // Set validation error state
    setValidationError(message) {
        this.hasValidationError = true;
        this.validationErrorMessage = message;
    }
    
    // Clear validation error state
    clearValidationError() {
        this.hasValidationError = false;
        this.validationErrorMessage = '';
    }

    // Event handlers
    handleLookupClick(event) {
        event.preventDefault();
        event.stopPropagation();

        if (this.isReadonly) {
            return;
        }

        if (this.isDropdownOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }

    handleInputFocus() {
        if (this.isReadonly) {
            return;
        }
        // Add focus styling or behavior if needed
    }

    handleInputBlur() {
        // Handle blur behavior if needed
    }

    handleKeyDown(event) {
        if (this.isReadonly) {
            return;
        }

        switch (event.key) {
            case 'Enter':
            case 'ArrowDown':
                event.preventDefault();
                if (!this.isDropdownOpen) {
                    this.openDropdown();
                }
                break;
            case 'Escape':
                event.preventDefault();
                if (this.isDropdownOpen) {
                    this.closeDropdown();
                }
                break;
            case 'Delete':
            case 'Backspace':
                if (this.selectedContactId) {
                    event.preventDefault();
                    this.clearSelection();
                }
                break;
            default:
                // No action for other keys
                break;
        }
    }

    handleClearSelection(event) {
        event.preventDefault();
        event.stopPropagation();
        this.clearSelection();
        this.focusInput();
    }

    handleContactSelect(event) {
        event.preventDefault();
        event.stopPropagation();

        const contactId = event.currentTarget.dataset.contactId;

        // Validate contact selection
        if (!contactId) {
            this.showToast('Selection Error', 'Invalid contact selection', 'error');
            return;
        }

        const selectedContact = this.contacts.find(contact => contact.Id === contactId);

        if (!selectedContact) {
            this.showToast('Selection Error', 'Selected contact is no longer available', 'error');
            this.refreshContacts();
            return;
        }

        // Validate contact data integrity
        if (!selectedContact.Name) {
            this.showToast('Data Error', 'Selected contact has invalid data', 'error');
            return;
        }

        this.selectedContactId = selectedContact.Id;
        this.selectedContactName = this.getContactDisplayLabel(selectedContact);
        this.closeDropdown();
        this.dispatchSelectionChange();

        // Return focus to input after selection
        setTimeout(() => {
            this.focusInput();
        }, 0);
    }

    handleContactKeyDown(event) {
        switch (event.key) {
            case 'Enter':
            case ' ':
                event.preventDefault();
                this.handleContactSelect(event);
                break;
            case 'Escape':
                event.preventDefault();
                this.closeDropdown();
                break;
            default:
                // No action for other keys
                break;
        }
    }

    handleClickOutside(event) {
        const component = this.template.querySelector('.dynamic-contact-lookup');
        if (component && !component.contains(event.target)) {
            this.closeDropdown();
        }
    }

    handleRetry() {
        // Clear all error states
        this.clearAllErrors();
        
        // Invalidate cache to force fresh data
        this.invalidateCache();
        
        // Add exponential backoff for retry attempts
        const retryDelay = this.calculateRetryDelay();
        this._retryAttempts = (this._retryAttempts || 0) + 1;
        
        setTimeout(() => {
            // Check if component is still connected before retrying
            if (this._isConnected) {
                this.loadContacts();
            }
        }, retryDelay);
    }

    clearAllErrors() {
        this.hasError = false;
        this.errorMessage = '';
        this.showRetry = false;
        this.networkError = false;
        this.permissionError = false;
        this.clearValidationError();
    }

    calculateRetryDelay() {
        if (!this._retryAttempts) {
            return 0;
        }
        const exponentialDelay = RETRY_BASE_DELAY_MS * Math.pow(RETRY_BACKOFF_EXPONENT, this._retryAttempts);
        return Math.min(exponentialDelay, RETRY_MAX_DELAY_MS);
    }

    // Dropdown management
    openDropdown() {
        // Validate prerequisites before opening dropdown
        if (this.isReadonly) {
            return;
        }

        if (!this.recordId) {
            this.setValidationError('Account context is required to load contacts.');
            this.showToast('Configuration Error', 'No account selected', 'warning');
            return;
        }

        if (!this.contactTypes || this.contactTypesArray.length === 0) {
            this.setValidationError('Contact types not configured for this field.');
            this.showToast('Configuration Error', 'Contact types not configured', 'warning');
            return;
        }

        // Clear any existing validation errors
        this.clearValidationError();

        this.isDropdownOpen = true;

        // Load contacts immediately when dropdown is opened
        this.loadContacts();
    }

    closeDropdown() {
        this.isDropdownOpen = false;
        this.clearDebounceTimeout();
    }

    // Contact loading
    loadContacts() {
        // Check cache first
        if (this.isCacheValid()) {
            this.contacts = this.processContactsForDisplay(this._contactsCache);
            return;
        }

        // Clear any existing debounce
        this.clearDebounceTimeout();

        // Debounce the actual loading
        this._debounceTimeout = setTimeout(() => {
            this.performContactLoad();
        }, DEBOUNCE_DELAY_MS);
    }

    performContactLoad() {
        if (!this.recordId || !this.contactTypes || this.contactTypes.length === 0) {
            this.contacts = [];
            this.isLoading = false;
            return;
        }

        // Prevent multiple simultaneous requests
        if (this.isLoading) {
            return;
        }

        this.isLoading = true;
        this.hasError = false;
        this.errorMessage = '';
        this.showRetry = false;

        // Store the current request to handle race conditions
        const currentRequestId = Date.now();
        this._currentRequestId = currentRequestId;

        getFilteredContacts({ 
            accountId: this.recordId, 
            recordTypes: this.contactTypesArray 
        })
        .then(result => {
            // Only process result if this is still the current request
            if (this._currentRequestId === currentRequestId) {
                this.handleLoadSuccess(result);
            }
        })
        .catch(error => {
            // Only handle error if this is still the current request
            if (this._currentRequestId === currentRequestId) {
                this.handleLoadError(error);
            }
        })
        .finally(() => {
            // Only update loading state if this is still the current request
            if (this._currentRequestId === currentRequestId) {
                this.isLoading = false;
            }
        });
    }

    handleLoadSuccess(result) {
        this._contactsCache = result || [];
        this._cacheTimestamp = Date.now();
        this.updateCacheContext();
        this.contacts = this.processContactsForDisplay(this._contactsCache);
        
        // Clear all error states on successful load
        this.clearAllErrors();
        
        // Reset retry attempts on successful load
        this._retryAttempts = 0;
        
        // Dispatch success event
        this.dispatchSuccessEvent();
    }

    handleLoadError(error) {
        this.hasError = true;
        this.contacts = [];
        this.networkError = false;
        this.permissionError = false;
        
        const errorMessage = this.extractErrorMessage(error);
        const errorType = this.categorizeError(errorMessage, error);
        
        this.applyErrorState(errorType, errorMessage);
        this.dispatchErrorEvent(errorType, this.errorMessage, error);
    }

    extractErrorMessage(error) {
        if (error.body?.message) {
            return error.body.message;
        }
        if (error.message) {
            return error.message;
        }
        if (error.body?.pageErrors?.length > 0) {
            return error.body.pageErrors[0].message;
        }
        if (error.body?.fieldErrors) {
            const fieldErrorKeys = Object.keys(error.body.fieldErrors);
            if (fieldErrorKeys.length > 0) {
                return error.body.fieldErrors[fieldErrorKeys[0]][0].message;
            }
        }
        return 'Failed to load contacts. Please try again.';
    }

    categorizeError(errorMessage, error) {
        const lowerErrorMessage = errorMessage.toLowerCase();
        
        if (this.isPermissionError(lowerErrorMessage)) {
            return 'permission';
        }
        if (this.isNetworkError(lowerErrorMessage, error)) {
            return 'network';
        }
        if (this.isTimeoutError(lowerErrorMessage, error)) {
            return 'timeout';
        }
        if (this.isInvalidAccountError(lowerErrorMessage)) {
            return 'invalid_account';
        }
        if (this.isLimitError(lowerErrorMessage)) {
            return 'limit';
        }
        
        return 'general';
    }

    isPermissionError(lowerErrorMessage) {
        return lowerErrorMessage.includes('permission') || 
               lowerErrorMessage.includes('access') || 
               lowerErrorMessage.includes('insufficient') ||
               lowerErrorMessage.includes('unauthorized');
    }

    isNetworkError(lowerErrorMessage, error) {
        return lowerErrorMessage.includes('network') || 
               lowerErrorMessage.includes('connection') ||
               lowerErrorMessage.includes('offline') ||
               error.name === 'NetworkError';
    }

    isTimeoutError(lowerErrorMessage, error) {
        return lowerErrorMessage.includes('timeout') || 
               lowerErrorMessage.includes('timed out') ||
               error.name === 'TimeoutError';
    }

    isInvalidAccountError(lowerErrorMessage) {
        return lowerErrorMessage.includes('invalid') && 
               lowerErrorMessage.includes('account');
    }

    isLimitError(lowerErrorMessage) {
        return lowerErrorMessage.includes('limit') || 
               lowerErrorMessage.includes('exceeded');
    }

    applyErrorState(errorType, originalMessage) {
        switch (errorType) {
            case 'permission':
                this.permissionError = true;
                this.errorMessage = 'You do not have permission to view contacts for this account.';
                this.showRetry = false;
                this.showToast('Access Denied', this.errorMessage, 'error');
                break;
            case 'network':
                this.networkError = true;
                this.errorMessage = 'Network error. Please check your connection and try again.';
                this.showRetry = true;
                break;
            case 'timeout':
                this.errorMessage = 'Request timed out. Please try again.';
                this.showRetry = true;
                break;
            case 'invalid_account':
                this.errorMessage = 'Invalid account context. Please refresh the page.';
                this.showRetry = false;
                break;
            case 'limit':
                this.errorMessage = 'Too many requests. Please wait a moment and try again.';
                this.showRetry = true;
                break;
            default:
                this.errorMessage = originalMessage;
                this.showRetry = true;
                break;
        }
    }
    
    // Dispatch error event to parent component
    dispatchErrorEvent(errorType, message, originalError) {
        const errorEvent = new CustomEvent('contacterror', {
            detail: {
                fieldName: this.fieldName,
                errorType: errorType,
                message: message,
                originalError: originalError,
                canRetry: this.showRetry
            }
        });
        this.dispatchEvent(errorEvent);
    }

    // Contact processing
    processContactsForDisplay(rawContacts) {
        if (!rawContacts || rawContacts.length === 0) {
            return [];
        }

        // Ensure contacts are properly sorted by name for consistent user experience
        const sortedContacts = [...rawContacts].sort((a, b) => {
            const nameA = (a.Name || '').toLowerCase();
            const nameB = (b.Name || '').toLowerCase();
            return nameA.localeCompare(nameB);
        });

        // Limit contacts to maximum display count for performance
        // Server already limits to 200, but this provides additional client-side protection
        return sortedContacts
            .slice(0, MAX_CONTACTS_DISPLAY)
            .map((contact, index) => ({
                ...contact,
                cssClass: this.getContactCssClass(contact.Id),
                isSelected: contact.Id === this.selectedContactId,
                displayLabel: this.getContactDisplayLabel(contact),
                recordTypeDisplay: this.getRecordTypeDisplayName(contact.RecordType?.DeveloperName),
                // Add index for efficient rendering and virtual scrolling if needed
                displayIndex: index
            }));
    }

    getContactCssClass(contactId) {
        let baseClass = 'contact-item';
        if (contactId === this.selectedContactId) {
            baseClass += ' selected';
        }
        return baseClass;
    }

    getContactDisplayLabel(contact) {
        let displayName = contact.Name;
        
        // Add record type information
        if (contact.RecordType && contact.RecordType.DeveloperName) {
            const recordTypeName = this.getRecordTypeDisplayName(contact.RecordType.DeveloperName);
            displayName += ` (${recordTypeName})`;
        }
        
        return displayName;
    }

    // Helper method to get user-friendly record type names
    getRecordTypeDisplayName(developerName) {
        switch (developerName) {
            case 'JJ_JPN_Doctor':
                return 'Dr';
            case 'Staff':
                return 'Staff';
            default:
                // Fallback to developer name
                return developerName;
        }
    }

    // Cache management
    isCacheValid() {
        if (!this._contactsCache || !this._cacheTimestamp) {
            return false;
        }
        
        // Check if account context has changed
        if (this._lastAccountId !== this.recordId) {
            return false;
        }
        
        // Check if contact types have changed
        const currentContactTypesStr = JSON.stringify(this.contactTypesArray.sort((a, b) => a.localeCompare(b)));
        const lastContactTypesStr = JSON.stringify((this._lastContactTypes || []).sort((a, b) => a.localeCompare(b)));
        if (currentContactTypesStr !== lastContactTypesStr) {
            return false;
        }
        
        // Check if cache has expired (5 minutes)
        const now = Date.now();
        return (now - this._cacheTimestamp) < CACHE_DURATION_MS;
    }

    invalidateCache() {
        this._contactsCache = null;
        this._cacheTimestamp = null;
        this._lastAccountId = null;
        this._lastContactTypes = null;
    }

    updateCacheContext() {
        this._lastAccountId = this.recordId;
        this._lastContactTypes = [...this.contactTypesArray];
    }

    // Focus management
    focusInput() {
        const input = this.template.querySelector('input');
        if (input) {
            input.focus();
        }
    }

    // Utility methods
    clearDebounceTimeout() {
        if (this._debounceTimeout) {
            clearTimeout(this._debounceTimeout);
            this._debounceTimeout = null;
        }
    }

    dispatchSelectionChange() {
        // Clear any validation errors on successful selection
        this.clearValidationError();
        
        // Dispatch custom event to parent component
        const selectionEvent = new CustomEvent('contactselect', {
            detail: {
                fieldName: this.fieldName,
                contactId: this.selectedContactId,
                contactName: this.selectedContactName,
                isValid: true
            }
        });
        this.dispatchEvent(selectionEvent);
    }
    
    // Dispatch success event for successful contact loading
    dispatchSuccessEvent() {
        const successEvent = new CustomEvent('contactsloaded', {
            detail: {
                fieldName: this.fieldName,
                contactCount: this.contacts.length,
                accountId: this.recordId,
                contactTypes: this.contactTypesArray
            }
        });
        this.dispatchEvent(successEvent);
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant
        });
        this.dispatchEvent(event);
    }

    // Handle contact link click for navigation
    handleContactLinkClick(event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (!this.selectedContactId) {
            return;
        }

        // Generate URL and open in new tab
        this[NavigationMixin.GenerateUrl]({
            type: 'standard__recordPage',
            attributes: {
                recordId: this.selectedContactId,
                objectApiName: 'Contact',
                actionName: 'view'
            }
        }).then(url => {
            window.open(url, '_blank');
        });
    }
}