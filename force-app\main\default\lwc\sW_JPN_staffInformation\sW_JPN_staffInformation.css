/* Staff Container with <PERSON><PERSON> - Similar to Doctor Con<PERSON>er */
.staff-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.staff-card {
    border: 1px solid #d8dde6;
    border-radius: 0.25rem;
    padding: 0.75rem;
    background-color: #ffffff;
    margin-bottom: 0.5rem;
}

/* Staff Header with Name and Menu */
.staff-header {
    align-items: center;
}

/* Staff Name Link Styling */
.staff-name-link {
    color: #0176d3;
    text-decoration: none;
    font-weight: 600;
}

.staff-name-link:hover {
    text-decoration: underline;
    color: #014486;
}

/* Table Styling for Better Alignment - Similar to Doctor Table */
.custom-staff-table {
    width: 100%;
    font-size: 0.8125rem;
}

.field-label-cell {
    font-weight: 600;
    color: #3e3e3c;
    width: 40%;
    padding: 0.25rem 0.5rem;
    border-right: 1px solid #d8dde6;
    background-color: #f3f3f3;
    font-size: 0.8125rem;
}

.field-value-cell {
    color: #181818;
    padding: 0.25rem 0.5rem;
    word-break: break-word;
    font-size: 0.8125rem;
}

/* Hover effects */
.staff-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
}

.slds-modal__container {
    max-width: 600px;
}

/* Hide the AccountId field in the form */
.slds-hide {
    display: none !important;
}

/* Ensure proper spacing for action buttons */
.slds-card [slot="actions"] .slds-grid {
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .slds-card [slot="actions"] .slds-grid {
        flex-direction: column;
    }
    
    .slds-card [slot="actions"] .slds-col {
        width: 100%;
    }
}

/* Field Alignment Fix */
.slds-form-element__control lightning-record-edit-form {
    display: block;
}

.slds-form-element__control lightning-input-field {
    display: block;
}

/* Ensure consistent height for both input fields */
.slds-form-element__control lightning-input,
.slds-form-element__control lightning-input-field {
    min-height: 2.25rem;
}

/* Align labels consistently */
.slds-form-element__label {
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
    line-height: 1.25;
    color: #3e3e3c;
}

/* Ensure form elements have consistent spacing */
.slds-form .slds-form-element {
    margin-bottom: 0;
}

/* Fix any margin issues with the record edit form */
.slds-form-element__control lightning-record-edit-form lightning-input-field {
    margin-bottom: 0;
}

/* Modal styling improvements */
.custom-modal-container {
    max-width: 700px;
}/* Scro
llbar styling - Consistent with Dr Information */
.staff-list::-webkit-scrollbar {
    width: 6px;
}

.staff-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.staff-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.staff-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
    .field-label-cell {
        width: 45%;
        font-size: 0.8125rem;
    }
    
    .field-value-cell {
        font-size: 0.8125rem;
    }
    
    .staff-list {
        max-height: 500px;
    }
}