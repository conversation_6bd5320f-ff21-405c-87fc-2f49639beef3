<template>
    <lightning-card title={labels.title} icon-name="standard:contact">
        <div slot="actions" class="slds-grid slds-gutters_xx-small">
            <div class="slds-col">
                <lightning-button 
                    variant="neutral" 
                    label={labels.newContact} 
                    onclick={handleNewContact}
                    icon-name="utility:add">
                </lightning-button>
            </div>
            <div class="slds-col">
                <lightning-button 
                    variant="brand" 
                    label={labels.addRelationship} 
                    onclick={handleAddRelationship}
                    icon-name="utility:link">
                </lightning-button>
            </div>
        </div>
        
        <div class="slds-card__body slds-card__body_inner">
            <template if:true={isLoading}>
                <div class="slds-align_absolute-center slds-p-around_medium">
                    <lightning-spinner alternative-text="Loading..." size="small"></lightning-spinner>
                </div>
            </template>
            
            <template if:false={isLoading}>
                <template if:true={hasStaffContacts}>
                    <div class="staff-list slds-scrollable_y" style="max-height: 400px;">
                        <template for:each={staffContacts} for:item="staff">
                            <div key={staff.Id} class="staff-card slds-box slds-theme_default slds-m-bottom_small">
                                <!-- Staff Name as Hyperlink with Edit Dropdown -->
                                <div class="staff-header slds-m-bottom_small slds-grid slds-grid_align-spread">
                                    <div class="slds-col">
                                        <h3 class="slds-text-heading_small">
                                            <a href={staff.contactUrl} 
                                               target="_blank"
                                               class="staff-name-link">
                                                {staff.Contact.Name}
                                            </a>
                                        </h3>
                                    </div>
                                    <div class="slds-col slds-no-flex">
                                        <lightning-button-menu 
                                            alternative-text="Show menu" 
                                            icon-size="x-small"
                                            menu-alignment="right">
                                            <lightning-menu-item 
                                                value="edit" 
                                                label={labels.edit}
                                                data-acr-id={staff.Id}
                                                onclick={handleEditContact}>
                                            </lightning-menu-item>
                                        </lightning-button-menu>
                                    </div>
                                </div>
                                
                                <!-- Staff Fields Table -->
                                <table class="slds-table slds-table_bordered slds-table_cell-buffer custom-staff-table">
                                    <tbody>
                                        <tr class="slds-hint-parent">
                                            <td class="field-label-cell">{labels.occupation}</td>
                                            <td class="field-value-cell">{staff.Contact.Occupation__c}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </template>
                    </div>
                </template>
                
                <template if:false={hasStaffContacts}>
                    <div class="slds-align_absolute-center slds-p-around_medium">
                        <div class="slds-text-color_weak">
                            <lightning-icon icon-name="utility:info" size="small" class="slds-m-right_small"></lightning-icon>
                            {labels.noStaff}
                        </div>
                    </div>
                </template>
            </template>
        </div>
    </lightning-card>

    <!-- Add Relationship Modal -->
    <template if:true={showAddRelationshipModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container custom-modal-container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" 
                            title="Close" onclick={handleCloseModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium slds-hyphenate">{labels.addRelationship}</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <!-- Account Contact Relationship Information Header -->
                    <div class="slds-section slds-is-open slds-m-bottom_medium">
                        <h3 class="slds-section__title slds-theme_shade">
                            <span class="slds-truncate slds-p-horizontal_small" title={labels.relationshipInfo}>
                                {labels.relationshipInfo}
                            </span>
                        </h3>
                    </div>

                    <!-- Account Contact Relation Form - Two Column Layout -->
                    <div class="slds-form slds-form_stacked">
                        <div class="slds-grid slds-gutters slds-wrap">
                            <!-- Account Field -->
                            <div class="slds-col slds-size_1-of-2 slds-max-medium-size_1-of-1">
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label" for="account-field">{labels.account}</label>
                                    <div class="slds-form-element__control">
                                        <lightning-input 
                                            id="account-field"
                                            type="text" 
                                            value={currentAccountName}
                                            readonly>
                                        </lightning-input>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Field - Standard Lookup -->
                            <div class="slds-col slds-size_1-of-2 slds-max-medium-size_1-of-1">
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label" for="contact-field">{labels.selectContact}</label>
                                    <div class="slds-form-element__control">
                                        <lightning-record-edit-form object-api-name="AccountContactRelation">
                                            <lightning-input-field 
                                                field-name="ContactId"
                                                variant="label-hidden"
                                                value={selectedContactId}
                                                onchange={handleContactSelection}>
                                            </lightning-input-field>
                                        </lightning-record-edit-form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Spinner -->
                    <template if:true={isLoading}>
                        <div class="slds-text-align_center slds-p-around_medium">
                            <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
                        </div>
                    </template>
                </div>
                <footer class="slds-modal__footer">
                    <button class="slds-button slds-button_neutral" onclick={handleCloseModal}>
                        {labels.cancel}
                    </button>
                    <button class="slds-button slds-button_brand" onclick={handleSaveRelationship}>
                        {labels.save}
                    </button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- Record Type Selection Modal -->
    <template if:true={showRecordTypeModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" 
                            title="Close" onclick={handleCloseModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium slds-hyphenate">{labels.newContact}</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <div class="slds-form-element">
                        <label class="slds-form-element__label slds-text-heading_small">
                            {labels.selectRecordType}
                        </label>
                        <div class="slds-form-element__control slds-p-top_medium">
                            <div class="slds-radio">
                                <input type="radio" id="staff-record-type" name="recordType" value="Staff" checked />
                                <label class="slds-radio__label" for="staff-record-type">
                                    <span class="slds-radio--faux"></span>
                                    <span class="slds-form-element__label slds-text-heading_small">{labels.staff}</span>
                                </label>
                            </div>
                            <div class="slds-text-body_small slds-text-color_weak slds-p-left_large">
                                {labels.recordTypeDescription}
                            </div>
                        </div>
                    </div>
                </div>
                <footer class="slds-modal__footer">
                    <button class="slds-button slds-button_neutral" onclick={handleCloseModal}>
                        {labels.cancel}
                    </button>
                    <button class="slds-button slds-button_brand" onclick={handleRecordTypeNext}>
                        {labels.next}
                    </button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>