import { LightningElement, api, track, wire } from 'lwc';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import { refreshApex } from '@salesforce/apex';
import getStaffContacts from '@salesforce/apex/JJ_JPN_ContactSidebarController.getStaffContacts';
import createAccountContactRelationWithType from '@salesforce/apex/JJ_JPN_ContactSidebarController.createAccountContactRelationWithType';
import getStaffRecordTypeId from '@salesforce/apex/JJ_JPN_ContactSidebarController.getStaffRecordTypeId';
import LANG from '@salesforce/i18n/lang';

// Account fields
import ACCOUNT_NAME_FIELD from '@salesforce/schema/Account.Name';

const ACCOUNT_FIELDS = [ACCOUNT_NAME_FIELD];
// Timeout delay for retry operations (in milliseconds)
const RETRY_DELAY_MS = 1000;
// Modal close delay to ensure smooth transition (in milliseconds)
const MODAL_CLOSE_DELAY_MS = 300;
// language label map (move out of class to reduce function complexity)
const LABELS = {
    en: {
        title: 'Staff Information',
        addRelationship: 'Add Relationship',
        newContact: 'New Contact',
        noStaff: 'No staff registered',
        name: 'Name',
        occupation: 'Occupation',
        save: 'Save',
        cancel: 'Cancel',
        selectContact: 'Select Contact',
        success: 'Success',
        staffAdded: 'Staff added successfully',
        error: 'Error',
        edit: 'Edit',
        selectRecordType: 'Select a record type',
        next: 'Next',
        staff: 'Staff',
        recordTypeDescription: 'Record type for Staff(Japan)',
        relationshipInfo: 'Account Contact Relationship Information',
        account: 'Account'
    },
    ja: {
        title: 'スタッフ情報（Dr.以外）',
        addRelationship: 'リレーションを追加',
        newContact: '新規取引先個人',
        noStaff: 'スタッフが登録されていません',
        name: '名前',
        occupation: '職業',
        save: '保存',
        cancel: 'キャンセル',
        selectContact: '連絡先を選択',
        success: '成功',
        staffAdded: 'スタッフが正常に追加されました',
        error: 'エラー',
        edit: '編集',
        selectRecordType: 'レコードタイプを選択',
        next: '次へ',
        staff: 'スタッフ',
        recordTypeDescription: 'スタッフ（日本）のレコードタイプ',
        relationshipInfo: '得意先と取引先個人のリレーション情報',
        account: '得意先'
    }
};

export default class SWJPNStaffInformation extends NavigationMixin(LightningElement) {
    @api recordId;
    @track staffContacts = [];
    @track isLoading = false;
    @track showAddRelationshipModal = false;
    @track showNewContactModal = false;
    @track showRecordTypeModal = false;
    @track selectedContactId = '';
    @track staffRecordTypeId = '';
    @track error;
    wiredStaffResult;

    // simplified labels getter to reduce cognitive complexity
    get labels() {
        return LANG === 'ja' ? LABELS.ja : LABELS.en;
    }

    @wire(getRecord, { recordId: '$recordId', fields: ACCOUNT_FIELDS })
    wiredAccount;

    @wire(getStaffContacts, { accountId: '$recordId' })
    wiredStaffContacts(result) {
        this.wiredStaffResult = result;
        if (result.data) {
            // Add contactUrl to each staff contact for opening in new tab
            this.staffContacts = result.data.map(staff => {
                return {
                    ...staff,
                    contactUrl: `/lightning/r/Contact/${staff.Contact.Id}/view`
                };
            });
            this.error = undefined;
        } else {
            // Handle error case
            this.error = result.error || null;
            this.staffContacts = [];
        }
    }

    connectedCallback() {
        this.loadStaffRecordTypeId();
    }

    loadStaffRecordTypeId() {
        getStaffRecordTypeId()
            .then(result => {
                this.staffRecordTypeId = result;
            })
            .catch(error => {
                const errorMsg = LANG === 'ja' ? 'スタッフレコードタイプの読み込みエラー: ' : 'Error loading staff record type: ';
                this.showToast(this.labels.error, errorMsg + error.body?.message, 'error');
            });
    }

    handleAddRelationship() {
        this.showAddRelationshipModal = true;
    }

    handleNewContact() {
        // Show the record type modal
        this.showRecordTypeModal = true;
    }
    
    navigateToNewContact() {
        // If we don't have the record type ID, try to load it first
        if (!this.staffRecordTypeId) {
            this.loadStaffRecordTypeId();
            
            // Wait a moment and try again
            setTimeout(() => {
                this.handleRecordTypeRetry();
            }, RETRY_DELAY_MS);
        } else {
            this.performNavigation();
        }
    }

    handleRecordTypeRetry() {
        if (this.staffRecordTypeId) {
            this.performNavigation();
        } else {
            const notFoundMsg = LANG === 'ja' ? 'スタッフレコードタイプが見つかりません。管理者にお問い合わせください。' : 'Staff record type not found. Please contact your administrator.';
            this.showToast(this.labels.error, notFoundMsg, 'error');
        }
    }
    
    performNavigation() {
        // Try using standard__webPage to avoid modal overlay issues
        const newContactUrl = `/lightning/o/Contact/new?recordTypeId=${this.staffRecordTypeId}&defaultFieldValues=AccountId=${this.recordId}`;
        
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: newContactUrl
            }
        }).catch(() => {
            // Fallback to direct URL navigation
            const baseUrl = window.location.origin;
            const fullUrl = `${baseUrl}${newContactUrl}`;
            window.location.assign(fullUrl);
        });
    }

    handleCloseModal() {
        this.showAddRelationshipModal = false;
        this.showNewContactModal = false;
        this.showRecordTypeModal = false;
        this.selectedContactId = '';
    }

    handleRecordTypeNext() {
        this.showRecordTypeModal = false;
        
        // Add a small delay to ensure modal closes before navigation
        setTimeout(() => {
            this.navigateToNewContact();
        }, MODAL_CLOSE_DELAY_MS);
    }

    handleContactSelection(event) {
        this.selectedContactId = event.target.value;
    }

    async handleSaveRelationship() {
        // Validate contact selection
        if (!this.validateContactSelection()) {
            return;
        }

        this.isLoading = true;
        
        try {
            await this.createStaffRelationship();
        } catch (error) {
            this.handleSaveError(error);
        } finally {
            this.isLoading = false;
        }
    }

    validateContactSelection() {
        if (!this.selectedContactId) {
            this.showToast(this.labels.error, LANG === 'ja' ? '連絡先を選択してください' : 'Please select a contact', 'error');
            return false;
        }

        // Validate that selectedContactId is a valid Salesforce ID
        if (!this.selectedContactId.match(/^[a-zA-Z0-9]{15}$|^[a-zA-Z0-9]{18}$/)) {
            const invalidIdMsg = LANG === 'ja' ? '無効な連絡先ID形式' : 'Invalid contact ID format';
            this.showToast(this.labels.error, invalidIdMsg, 'error');
            return false;
        }

        return true;
    }

    async createStaffRelationship() {
        const result = await createAccountContactRelationWithType({
            accountId: this.recordId,
            contactId: this.selectedContactId,
            relationshipType: 'Staff'
        });

        if (result === 'Success') {
            this.showToast(this.labels.success, this.labels.staffAdded, 'success');
            this.handleCloseModal();
            await refreshApex(this.wiredStaffResult);
        } else {
            const warningTitle = LANG === 'ja' ? '警告' : 'Warning';
            const translatedMsg = this.translateApexError(result);
            this.showToast(warningTitle, translatedMsg, 'warning');
        }
    }

    handleSaveError(error) {
        if (error.body?.message) {
            errorMessage = error.body.message;
        } else if (error.message) {
            errorMessage = error.message;
        } else {
        // Explicit else to satisfy SonarQube
        errorMessage = LANG === 'ja'
            ? '不明なエラーが発生しました'
            : 'An unknown error occurred';
        }
        
        this.showToast(this.labels.error, errorMessage, 'error');
    }

    // Unused parameter renamed to indicate intentional non-use
    handleContactCreated(_event) {
        // Refresh the staff contacts list after new contact is created
        this.handleCloseModal();
        this.showToast(this.labels.success, LANG === 'ja' ? '新しい連絡先が作成されました' : 'New contact created successfully', 'success');
    }

    handleEditContact(event) {
        event.stopPropagation();
        const acrId = event.target.dataset.acrId;
        
        if (acrId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: {
                    recordId: acrId,
                    objectApiName: 'AccountContactRelation',
                    actionName: 'edit'
                }
            });
        } else {
            const errorMsg = LANG === 'ja' ? '編集するリレーションレコードが見つかりません' : 'Unable to find relationship record to edit';
            this.showToast(this.labels.error, errorMsg, 'error');
        }
    }

    // Translate Apex error messages to Japanese
    translateApexError(errorMsg) {
        if (LANG === 'ja') {
            // Map of English error messages to Japanese translations
            const translations = {
                'Contact not found': '連絡先が見つかりません',
                'Only Staff contacts can be added to this relationship': 'このリレーションにはスタッフの連絡先のみ追加できます',
                'Only Doctor contacts can be added to this relationship': 'このリレーションにはドクターの連絡先のみ追加できます',
                'Relationship already exists between this contact and account': 'この連絡先とアカウント間のリレーションは既に存在します',
                'Relationship already exists': 'リレーションは既に存在します',
                'Missing required parameters': '必須パラメータが不足しています',
                'Selected contact has record type': '選択された連絡先のレコードタイプ'
            };
            
            // Check for exact match
            if (translations[errorMsg]) {
                return translations[errorMsg];
            }
            
            // Check for partial matches and replace
            let translatedMsg = errorMsg;
            for (const [english, japanese] of Object.entries(translations)) {
                if (translatedMsg.includes(english)) {
                    translatedMsg = translatedMsg.replace(english, japanese);
                }
            }
            
            return translatedMsg;
        }
        
        return errorMsg;
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant
        });
        this.dispatchEvent(event);
    }

    get hasStaffContacts() {
        return this.staffContacts && this.staffContacts.length > 0;
    }

    get currentAccountName() {
        return getFieldValue(this.wiredAccount.data, ACCOUNT_NAME_FIELD);
    }
}