<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>JJ_JPN_Key_Person_1__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <description>Key Person 1 - KeyMan-1 名前 - 最もCLに影響度の大きいステークホルダー（Drと被ってもOK）</description>
    <externalId>false</externalId>
    <inlineHelpText>最もCLに影響度の大きいステークホルダー（Drと被ってもOK）</inlineHelpText>
    <label>Key Person 1</label>
    <lookupFilter>
        <active>true</active>
        <booleanFilter>1 AND 2</booleanFilter>
        <filterItems>
            <field>Contact.RecordType.DeveloperName</field>
            <operation>equals</operation>
            <value>Staff,JJ_JPN_Doctor</value>
        </filterItems>
        <filterItems>
            <field>$Source.JJ_JPN_Account__c</field>
            <operation>equals</operation>
            <valueField>Contact.AccountId</valueField>
        </filterItems>
        <infoMessage>Only <PERSON> and Doctor contacts related to this Account are available</infoMessage>
        <isOptional>true</isOptional>
    </lookupFilter>
    <referenceTo>Contact</referenceTo>
    <relationshipLabel>Account Profiles (Key Person 1)</relationshipLabel>
    <relationshipName>AccountProfiles_KeyPerson1</relationshipName>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Lookup</type>
</CustomField>
