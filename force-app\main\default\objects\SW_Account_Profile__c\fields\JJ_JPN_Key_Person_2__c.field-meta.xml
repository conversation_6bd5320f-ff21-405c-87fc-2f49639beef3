<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>JJ_JPN_Key_Person_2__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <description>Key Person 2 - KeyMan-2 名前 - 2番目にCLに影響度の大きいステークホルダー（Drと被ってもOK）</description>
    <externalId>false</externalId>
    <inlineHelpText>2番目にCLに影響度の大きいステークホルダー（Drと被ってもOK）</inlineHelpText>
    <label>Key Person 2</label>
    <lookupFilter>
        <active>true</active>
        <booleanFilter>1 AND 2</booleanFilter>
        <filterItems>
            <field>Contact.RecordType.DeveloperName</field>
            <operation>equals</operation>
            <value>Staff,JJ_<PERSON>N_Doctor</value>
        </filterItems>
        <filterItems>
            <field>$Source.JJ_JPN_Account__c</field>
            <operation>equals</operation>
            <valueField>Contact.AccountId</valueField>
        </filterItems>
        <infoMessage>Only Staff and Doctor contacts related to this Account are available</infoMessage>
        <isOptional>false</isOptional>
    </lookupFilter>
    <referenceTo>Contact</referenceTo>
    <relationshipLabel>Account Profiles (Key Person 2)</relationshipLabel>
    <relationshipName>AccountProfiles_KeyPerson2</relationshipName>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Lookup</type>
</CustomField>
